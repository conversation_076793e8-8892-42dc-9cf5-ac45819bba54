# MapMarker 地图标记工具

## 概述

MapMarker是一个基于ArcGIS的地图标记工具组件，参照ArcBRTools的规范设计，用于在地图上创建、编辑和删除各种类型的标记。

## 功能特性

### 1. 标记类型
- **点标记**：在地图上创建点位标记
- **线标记**：在地图上创建线性标记
- **面标记**：在地图上创建区域标记

### 2. 操作模式
- **创建模式**：默认模式，点击标记类型按钮后自动开始创建
- **编辑模式**：可以选择和编辑已有的标记
- **自动切换**：创建完成后自动开始下一个标记的创建

### 3. 样式配置
- **标签设置**：为标记添加文字标签
- **颜色选择**：6种预设颜色（红、蓝、绿、黄、紫、橙）
- **大小设置**：3种尺寸（小、中、大）

### 4. 管理功能
- **标记计数**：实时显示当前标记总数
- **批量清除**：一键清除所有标记
- **编辑支持**：点击标记进行编辑

## 技术实现

### 组件结构
```
MapMarker.vue
├── 模板部分 (Template)
│   └── Form组件配置界面
├── 脚本部分 (Script)
│   ├── 状态管理
│   ├── 绘制逻辑
│   ├── 符号创建
│   └── 事件处理
└── 样式部分 (Style)
    └── 界面样式定义
```

### 核心依赖
- `@/utils/MapHelper` - 地图辅助工具
- `@/hooks/arcgis` - ArcGIS相关hooks
- `@arcgis/core` - ArcGIS核心库
- Element Plus Form组件

### 状态管理
```typescript
const state = reactive({
  creating: boolean,        // 是否正在创建
  markerType: string,       // 当前标记类型
  editMode: boolean,        // 是否编辑模式
  selectedGraphic: Graphic, // 选中的图形
  markers: Graphic[]        // 所有标记
});
```

## 使用方法

### 1. 在ArcBRTools中集成
```vue
<!-- 添加工具按钮 -->
<ArcWidgetButton
  id="tool-mapmarker"
  :icon="'mdi:map-marker-plus'"
  :title="'地图标记'"
  @click="handleToolClick('mapmarker', '地图标记', isCollapsed)"
/>

<!-- 添加工具面板 -->
<MapMarker
  v-if="state.toolPanelOperate === 'mapmarker'"
  :view="view"
/>
```

### 2. 独立使用
```vue
<template>
  <MapMarker :view="mapView" />
</template>
```

## 操作流程

### 创建标记
1. 选择标记类型（点/线/面）
2. 设置标记样式（颜色、大小、标签）
3. 在地图上绘制图形
4. 自动创建标记并开始下一个

### 编辑标记
1. 点击"编辑模式"按钮
2. 点击地图上的标记选中
3. 拖拽节点进行编辑
4. 完成编辑

### 管理标记
- 查看标记总数
- 清除所有标记
- 切换操作模式

## 符号样式

### 点符号
- 圆形标记
- 白色边框
- 可配置颜色和大小

### 线符号
- 实线样式
- 可配置颜色和宽度

### 面符号
- 实填充样式
- 30%透明度
- 有色边框

## 扩展性

### 添加新的标记类型
1. 在`markerType`中添加新类型
2. 在`setMarkerType`中添加处理逻辑
3. 创建对应的符号生成函数

### 自定义样式
1. 扩展颜色选项
2. 添加更多尺寸选择
3. 支持自定义符号

### 数据持久化
- 可扩展标记数据的保存功能
- 支持导入/导出标记数据
- 与后端API集成

## 注意事项

1. **坐标系统**：使用地图视图的坐标系统
2. **图层管理**：标记存储在独立的GraphicsLayer中
3. **内存管理**：组件销毁时自动清理资源
4. **事件处理**：正确处理绘制和编辑事件
5. **用户体验**：提供清晰的操作反馈

## 兼容性

- ArcGIS API for JavaScript 4.x
- Vue 3.x
- Element Plus
- 现代浏览器支持