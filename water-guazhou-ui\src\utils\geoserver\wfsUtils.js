import { request } from '@/plugins/axios/geoserver';
// import * as webMercatorUtils from "@arcgis/core/geometry/support/webMercatorUtils.js";
const geoserverUrl = '/geoserver/guazhou/wfs';

/**
 * 前端属性过滤函数 - 支持中文字段名和中文值
 * @param {Array} features 要素数组
 * @param {string} condition 过滤条件，如 "管材 = 'PE'"
 * @returns {Array} 过滤后的要素数组
 */
const filterFeaturesByCondition = (features, condition) => {
  try {
    console.log('开始前端属性过滤，条件:', condition);

    if (!condition || condition === '1=1') {
      return features;
    }

    // 简单的条件解析器，支持常见的SQL条件
    return features.filter((feature) => {
      try {
        return evaluateCondition(feature.properties, condition);
      } catch (error) {
        console.warn('条件评估失败:', error, '要素:', feature.properties);
        return false;
      }
    });
  } catch (error) {
    console.error('前端属性过滤失败:', error);
    return features;
  }
};

/**
 * 评估单个条件
 * @param {Object} properties 要素属性
 * @param {string} condition 条件字符串
 * @returns {boolean} 是否满足条件
 */
const evaluateCondition = (properties, condition) => {
  // 移除多余的空格
  condition = condition.trim();

  // 处理 AND 和 OR 逻辑
  if (condition.includes(' and ') || condition.includes(' AND ')) {
    const parts = condition.split(/ and | AND /i);
    return parts.every((part) => evaluateCondition(properties, part.trim()));
  }

  if (condition.includes(' or ') || condition.includes(' OR ')) {
    const parts = condition.split(/ or | OR /i);
    return parts.some((part) => evaluateCondition(properties, part.trim()));
  }

  // 移除括号
  condition = condition.replace(/^\(|\)$/g, '').trim();

  // 处理 LIKE 条件
  if (condition.includes(' like ') || condition.includes(' LIKE ')) {
    const [field, value] = condition.split(/ like | LIKE /i);
    const fieldName = field.trim();
    const pattern = value.trim().replace(/'/g, '');
    const fieldValue = String(properties[fieldName] || '');

    // 简单的通配符匹配
    const regex = new RegExp(pattern.replace(/%/g, '.*'), 'i');
    return regex.test(fieldValue);
  }

  // 处理其他比较操作符
  const operators = ['>=', '<=', '<>', '!=', '>', '<', '='];

  for (const op of operators) {
    if (condition.includes(` ${op} `)) {
      const [field, value] = condition.split(` ${op} `);
      const fieldName = field.trim();
      const compareValue = value.trim().replace(/'/g, '');
      const fieldValue = properties[fieldName];

      switch (op) {
        case '=':
          return String(fieldValue) === compareValue;
        case '<>':
        case '!=':
          return String(fieldValue) !== compareValue;
        case '>':
          return Number(fieldValue) > Number(compareValue);
        case '<':
          return Number(fieldValue) < Number(compareValue);
        case '>=':
          return Number(fieldValue) >= Number(compareValue);
        case '<=':
          return Number(fieldValue) <= Number(compareValue);
      }
    }
  }

  return true;
};

/**
 * 前端空间过滤函数
 * @param {Array} features 要素数组
 * @param {Array} polygonCoordinates 多边形坐标
 * @returns {Array} 过滤后的要素数组
 */
const filterFeaturesByPolygon = (features, polygonCoordinates) => {
  try {
    console.log('开始前端空间过滤');

    if (!polygonCoordinates || polygonCoordinates.length === 0) {
      return features;
    }

    // 简单的点在多边形内判断（适用于点要素）
    return features.filter((feature) => {
      if (!feature.geometry) return false;

      // 处理点几何
      if (feature.geometry.type === 'Point') {
        const [x, y] = feature.geometry.coordinates;
        return isPointInPolygon([x, y], polygonCoordinates);
      }

      // 对于线和面几何，简单检查是否有交集（可以进一步优化）
      if (feature.geometry.type === 'LineString') {
        return feature.geometry.coordinates.some((coord) =>
          isPointInPolygon(coord, polygonCoordinates)
        );
      }

      // 默认包含
      return true;
    });
  } catch (error) {
    console.error('前端空间过滤失败:', error);
    return features;
  }
};

/**
 * 判断点是否在多边形内（射线法）
 * @param {Array} point [x, y]
 * @param {Array} polygon [[x1,y1], [x2,y2], ...]
 * @returns {boolean}
 */
const isPointInPolygon = (point, polygon) => {
  const [x, y] = point;
  let inside = false;

  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const [xi, yi] = polygon[i];
    const [xj, yj] = polygon[j];

    if (yi > y !== yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi) {
      inside = !inside;
    }
  }

  return inside;
};
export const QueryByPolygon = (layerName, polygonCoordinates, where) => {
  try {
    let filter = '1=1';
    if (polygonCoordinates) {
      const gmlPolygon = polygonCoordinates
        .map((coord) => `${coord[0]} ${coord[1]}`)
        .join(',');
      filter = `intersects(geom,POLYGON((${gmlPolygon})))`;
    }
    if (where) {
      filter += ` AND (${where})`;
    }

    // 对图层名和过滤条件进行URL编码，解决中文字符问题
    const encodedLayerName = encodeURIComponent(layerName);
    const encodedFilter = encodeURIComponent(filter);

    const url = `${geoserverUrl}?SERVICE=WFS&VERSION=2.0.0&REQUEST=GetFeature&typeName=${encodedLayerName}&outputFormat=application/json&SRS=EPSG:3857&CQL_FILTER=${encodedFilter}`;

    console.log('QueryByPolygon请求URL:', url);
    console.log('原始过滤条件:', filter);

    return request({
      url,
      method: 'get'
    });
  } catch (error) {
    console.error('QueryByPolygon执行失败:', error);
    throw error;
  }
};

export const GetFieldConfig = (layerName) => {
  const url = `${geoserverUrl}?SERVICE=WFS&VERSION=1.0.0&REQUEST=DescribeFeatureType&typeName=${layerName}&outputFormat=application/json`;
  return request({
    url,
    method: 'get'
  });
};

/**
 * 获取字段唯一值
 */
export const GetFieldValueByGeoserver = (params) => {
  return request({
    url: `/geoserver/ows?service=WFS&version=1.1.0&request=GetFeature&typeName=${params.layerName}&propertyName=${params.fiedName}&outputFormat=application/json`,
    method: 'get'
  });
};

/**
 * 按字段分组统计管长
 * @param {string} layerName 图层名
 * @param {string} groupField 分组字段名
 * @param {string} lengthField 管长字段名
 * @param {string} [where] 属性过滤条件
 * @param {Array} [polygonCoordinates] 空间圈选坐标
 * @returns {Promise<Array<{name: string, value: number}>>}
 */
export const groupLengthStatisticsByField = async (
  layerName,
  groupField,
  lengthField,
  where,
  polygonCoordinates
) => {
  try {
    console.log('开始执行长度统计:', {
      layerName,
      groupField,
      lengthField,
      where,
      polygonCoordinates
    });

    // 智能判断是否包含中文字符
    const containsChinese = (str) => {
      return /[\u4e00-\u9fa5]/.test(str);
    };

    let url;
    let useServerFilter = false;

    // 构建过滤条件
    let filter = '1=1';

    // 添加空间过滤
    if (polygonCoordinates) {
      const gmlPolygon = polygonCoordinates
        .map((coord) => `${coord[0]} ${coord[1]}`)
        .join(',');
      filter = `intersects(geom,POLYGON((${gmlPolygon})))`;
    }

    // 添加属性过滤 - 只有不包含中文字符时才尝试服务端过滤
    if (where && where !== '1=1') {
      if (!containsChinese(where)) {
        // 英文字段和值，可以尝试服务端过滤
        filter += ` AND (${where})`;
        useServerFilter = true;
        console.log('长度统计使用服务端过滤，条件:', filter);
      } else {
        // 包含中文字符，使用前端过滤
        console.log('长度统计检测到中文字符，将使用前端过滤');
        useServerFilter = false;
      }
    } else {
      useServerFilter = true;
    }

    const encodedLayerName = encodeURIComponent(layerName);

    if (useServerFilter && filter !== '1=1') {
      // 尝试服务端过滤
      const encodedFilter = encodeURIComponent(filter);
      url = `${geoserverUrl}?SERVICE=WFS&VERSION=2.0.0&REQUEST=GetFeature&typeName=${encodedLayerName}&outputFormat=application/json&SRS=EPSG:3857&CQL_FILTER=${encodedFilter}`;
    } else {
      // 获取所有数据，前端过滤
      url = `${geoserverUrl}?SERVICE=WFS&VERSION=2.0.0&REQUEST=GetFeature&typeName=${encodedLayerName}&outputFormat=application/json&SRS=EPSG:3857`;
    }

    console.log('GeoServer长度统计请求URL:', url);
    console.log('使用服务端过滤:', useServerFilter);
    console.log('过滤条件:', filter);

    const res = await request({ url, method: 'get' });

    // 检查响应是否为XML错误
    if (typeof res.data === 'string' && res.data.includes('<?xml')) {
      console.error('GeoServer返回XML错误:', res.data);
      // 如果服务端过滤失败，回退到前端过滤
      if (useServerFilter) {
        console.log('长度统计服务端过滤失败，回退到前端过滤');
        const fallbackUrl = `${geoserverUrl}?SERVICE=WFS&VERSION=2.0.0&REQUEST=GetFeature&typeName=${encodedLayerName}&outputFormat=application/json&SRS=EPSG:3857`;
        const fallbackRes = await request({ url: fallbackUrl, method: 'get' });

        let features = fallbackRes.data.features || [];
        console.log(`长度统计回退请求获取到 ${features.length} 条原始数据`);

        // 前端进行属性过滤
        if (where && where !== '1=1') {
          features = filterFeaturesByCondition(features, where);
          console.log(`长度统计前端过滤后剩余 ${features.length} 条数据`);
        }

        // 前端进行空间过滤
        if (polygonCoordinates) {
          features = filterFeaturesByPolygon(features, polygonCoordinates);
          console.log(`长度统计空间过滤后剩余 ${features.length} 条数据`);
        }

        // 前端分组统计管长
        const groupMap = new Map();
        features.forEach((f) => {
          const groupValue = f.properties[groupField];
          const lengthValue = parseFloat(f.properties[lengthField]) || 0;
          const key = groupValue?.toString() || '未知';
          
          if (groupMap.has(key)) {
            groupMap.set(key, groupMap.get(key) + lengthValue);
          } else {
            groupMap.set(key, lengthValue);
          }
        });

        const result = Array.from(groupMap.entries()).map(([name, value]) => ({
          name,
          value
        }));

        console.log('长度统计结果:', result);
        return result;
      }
      throw new Error('GeoServer返回错误响应');
    }

    // 正常响应处理
    let features = res.data.features || [];
    console.log(`长度统计获取到 ${features.length} 条数据`);

    // 如果使用了前端过滤，进行过滤处理
    if (!useServerFilter) {
      // 前端进行属性过滤
      if (where && where !== '1=1') {
        features = filterFeaturesByCondition(features, where);
        console.log(`长度统计前端过滤后剩余 ${features.length} 条数据`);
      }

      // 前端进行空间过滤
      if (polygonCoordinates) {
        features = filterFeaturesByPolygon(features, polygonCoordinates);
        console.log(`长度统计空间过滤后剩余 ${features.length} 条数据`);
      }
    }

    // 分组统计管长
    const groupMap = new Map();
    features.forEach((f) => {
      const groupValue = f.properties[groupField];
      const lengthValue = parseFloat(f.properties[lengthField]) || 0;
      const key = groupValue?.toString() || '未知';
      
      if (groupMap.has(key)) {
        groupMap.set(key, groupMap.get(key) + lengthValue);
      } else {
        groupMap.set(key, lengthValue);
      }
    });

    const result = Array.from(groupMap.entries()).map(([name, value]) => ({
      name,
      value
    }));

    console.log('长度统计结果:', result);
    return result;
  } catch (error) {
    console.error('groupLengthStatisticsByField执行失败:', error);
    throw error;
  }
};

