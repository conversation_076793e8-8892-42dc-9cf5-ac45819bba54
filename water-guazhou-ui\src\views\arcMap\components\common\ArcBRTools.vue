<template>
  <div class="tools-temp-wrapper">
    <ArcWidgetButton
      id="tool-pipelength"
      ref="refArcWidgetButton-tool-pipelength"
      :icon="'mdi:ruler'"
      :title="'管线长度'"
      @click="
        (isCollapsed) =>
          handleToolClick('pipelength', '管线长度测量', isCollapsed)
      "
    ></ArcWidgetButton>
    <ArcWidgetButton
      id="tool-areameasure"
      ref="refArcWidgetButton-tool-areameasure"
      :icon="'gis:measure-area-alt'"
      :title="'地图测量'"
      @click="
        (isCollapsed) => handleToolClick('areameasure', '地图测量', isCollapsed)
      "
    ></ArcWidgetButton>
    <ArcWidgetButton
      id="tool-mapmarker"
      ref="refArcWidgetButton-tool-mapmarker"
      :icon="'mdi:map-marker-plus'"
      :title="'地图标记'"
      @click="
        (isCollapsed) => handleToolClick('mapmarker', '地图标记', isCollapsed)
      "
    ></ArcWidgetButton>
    <div
      id="gis-overview"
      :title="'鹰眼图'"
      class="esri-widget esri-expand esri-component esri-widget--button custom-toolbar"
      @click="() => (state.showOverViewMap = !state.showOverViewMap)"
    >
      <Icon
        class="tool-icon"
        :icon="
          state.showOverViewMap === false ? 'gis:earth' : 'ep:d-arrow-right'
        "
      ></Icon>
    </div>
  </div>
  <div
    v-show="state.showOverViewMap"
    id="overviewmap"
    class="overviewmap"
  ></div>
  <Panel
    ref="refToolPanel"
    :custom-class="'tool-panel'"
    :telport="'#arcmap-wrapper'"
    :title="state.toolPanelTitle"
    :destroy-by-close="true"
    :before-close="() => handleToolPanelClose()"

  >
    <PipeLength
      v-if="state.toolPanelOperate === 'pipelength'"
      :view="view"
    ></PipeLength>
    <AreaMeasure
      v-if="state.toolPanelOperate === 'areameasure'"
      :view="view"
    ></AreaMeasure>
    <MapMarker
      v-if="state.toolPanelOperate === 'mapmarker'"
      :view="view"
    ></MapMarker>
  </Panel>
</template>
<script lang="ts" setup>
import useWidgets from '@/hooks/arcgis/useWidgets';
import useLayer from '@/hooks/arcgis/useLayer';
import SpatialReference from '@arcgis/core/geometry/SpatialReference.js';
import {
  useOverViewMap,
  useZoomBar,
  useHomeBar,
  usePrintBar,
  useBasemapGallary,
  useLegend
} from '@/hooks/arcgis';
import { queryLayerClassName } from '@/api/mapservice';
import { GetFieldUniqueValue } from '@/api/mapservice/fieldconfig';
import { GetFieldValueByGeoserver } from '@/utils/geoserver/wfsUtils';

import { usePipeLineGroup } from '@/hooks/arcgis/usePipelineGroup';
import { Icon } from '@iconify/vue';
// MapMarker组件会自动导入

const refToolPanel = ref<IPanelIns>();
const { proxy }: any = getCurrentInstance();
const { createServiceLayer } = useLayer();
const props = defineProps<{
  view?: __esri.MapView;
  basemapChange?: (e: any) => any;
}>();
const state = reactive<{
  showOverViewMap: boolean;
  toolPanelTitle: string;
  toolPanelOperate: string;
}>({
  showOverViewMap: false,
  toolPanelTitle: '',
  toolPanelOperate: ''
});
const { addCustomWidget } = useWidgets();
const printBar = usePrintBar();
const home = useHomeBar();
// const compass = useCompass()
const overViewMap = useOverViewMap();
const baseMapGallary = useBasemapGallary(props.basemapChange);
const zoom = useZoomBar();
const legend = useLegend();
const handleToolClick = (
  path: string,
  title: string,
  isCollapsed: boolean,
  fromPanel?: boolean
) => {
  if (!isCollapsed) {
    state.toolPanelOperate = path;
    state.toolPanelTitle = title;
    refToolPanel.value?.Open();
    customTools.map((item) => {
      if (item !== state.toolPanelOperate) {
        proxy.$refs['refArcWidgetButton-tool-' + item]?.toggle(true);
      }
    });
  } else {
    !fromPanel && refToolPanel.value?.Close();
  }
};
const customTools = ['areameasure', 'pipelength', 'mapmarker'];
const handleToolPanelClose = () => {
  // const refIns = proxy.$refs['refArcWidgetButton-' + state.toolPanelOperate]
  // refIns?.toggle && refIns.toggle(true)
  customTools.map((item) => {
    proxy.$refs['refArcWidgetButton-tool-' + item]?.toggle(true);
  });
};

const init = async (
  options?: {
    hidePipe?: boolean;
  },
  callBack?: () => void
) => {
  if (!props.view) return;
  // compass.init(props.view, 'bottom-right')
  zoom.init(props.view, 'bottom-right');
  addCustomWidget(props.view, 'tool-pipelength', 'bottom-right');
  addCustomWidget(props.view, 'tool-areameasure', 'bottom-right');
  addCustomWidget(props.view, 'tool-mapmarker', 'bottom-right');
  printBar.init(props.view, '', 'bottom-right');
  legend.init(props.view, 'bottom-right');
  baseMapGallary.init(props.view);
  overViewMap.init(props.view, 'overviewmap');
  addCustomWidget(props.view, 'gis-overview', 'bottom-right');
  const homeButton = props.view && home.init(props.view);
  if (options?.hidePipe !== true) {
    let pipelayer;
    if (GIS_SERVER_SWITCH) {
      pipelayer = createServiceLayer(props.view, 'WMSLayer', {
        id: 'pipelayer',
        title: '给水管网',
        url: '/geoserver/guazhou/wms',
        sublayers: [
          // { name: 'anqing' },
          { name: '管线', type: 'LineString', spatialReferences: 'EPSG:3857' },
          { name: '测点', type: 'Point', spatialReferences: 'EPSG:3857' },
          { name: '测流井', type: 'Point', spatialReferences: 'EPSG:3857' }
          // { name: '计量装置',type:'Point',spatialReferences:'EPSG:3857'},
          // { name: '阀门',type:'Point',spatialReferences:'EPSG:3857' },
          // { name: '非控制阀' ,type:'Point',spatialReferences:'EPSG:3857'},
          // { name: '消防栓',type:'Point',spatialReferences:'EPSG:3857' },
          // { name: '检查井',type:'Point',spatialReferences:'EPSG:3857'},
        ],
        // spatialReference: SpatialReference.WGS84, // 指定坐标系为 EPSG:4326
        // customParameters: {
        //   // WIDTH:539,
        //   // HEIGHT:768,
        //   // LAYERS: 'anqing',
        //   // BBOX:'-180,-90,180,90',
        //   // REQUEST: 'GetMap',
        //   SRS: "EPSG:4326" // 关键参数：指定坐标系为 EPSG:4326
        // },
        visible: true,
        version: '1.1.0', // WMS 版本
        format: 'png', // 图像格式
        transparent: false // 是否透明
      }) as __esri.MapImageLayer;
    } else {
      pipelayer = createServiceLayer(props.view, 'MapImage', {
        id: 'pipelayer',
        title: '给水管网',
        url:
          window.SITE_CONFIG.GIS_CONFIG.gisService +
          window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService
      }) as __esri.MapImageLayer;
    }
    props.view.map.add(pipelayer);
    await pipelayer?.when();
    const group = usePipeLineGroup('管线材质分组');
    const group1 = usePipeLineGroup('管线口径分组');
    const layerIds: number[] = pipelayer?.allSublayers
      .map((item) => item.id)
      .toArray();
    const layerRes = await queryLayerClassName(layerIds);
    const layerInfos = layerRes.data?.result?.rows || [];
    const layerId = layerInfos.find(
      (item) => item.geometrytype === 'esriGeometryPolyline'
    )?.layerid;
    if (GIS_SERVER_SWITCH) {
      GetFieldValueByGeoserver({
        layerName: '给水管线',
        fiedName: '材质'
      }).then((res) => {
        let data = res.data;
        // 创建一个空集合来存储唯一的MATERIAL值
        const uniqueMaterials = new Set();
        // 遍历特征数组
        data.features.forEach((feature) => {
          // 添加MATERIAL值到集合中
          uniqueMaterials.add(feature.properties['材质']);
        });
        group.init(props.view);
        // 将集合转换为数组（如果需要数组格式）
        const uniqueMaterialsArray = Array.from(uniqueMaterials);
        uniqueMaterialsArray.map((item, i) => {
          const sql = "材质 = '" + item + "'";
          const color = group.genColor(i);
          group.addSubLayerByGeoServer(item, data, color, sql);
        });
      });
      GetFieldValueByGeoserver({
        layerName: '给水管线',
        fiedName: '材质'
      }).then((res) => {
        let data = res.data;
        // 创建一个空集合来存储唯一的MATERIAL值
        const uniqueMaterials = new Set();
        // 遍历特征数组
        data.features.forEach((feature) => {
          // 添加MATERIAL值到集合中
          uniqueMaterials.add(feature.properties.DIAMETER);
        });
        group1.init(props.view);
        // 将集合转换为数组（如果需要数组格式）
        const uniqueMaterialsArray = Array.from(uniqueMaterials);
        uniqueMaterialsArray.map((item, i) => {
          const sql = 'DIAMETER = ' + item;
          const color = group1.genColor(i);
          group1.addSubLayerByGeoServer(item, data, color, sql);
        });
      });
    } else {
      GetFieldUniqueValue({
        layerid: layerId,
        field_name: 'MATERIAL'
      }).then((res) => {
        group.init(props.view);
        const material = res.data?.result?.rows || [];
        material.map((item, i) => {
          const sql = group.genSql('MATERIAL', item);
          const color = group.genColor(i);
          group.addSubLayer(props.view, layerId, item, sql, color);
        });
      });
      GetFieldUniqueValue({
        layerid: layerId,
        field_name: 'DIAMETER'
      }).then((res) => {
        group1.init(props.view);
        const material = res.data?.result?.rows || [];
        material.map((item, i) => {
          const sql = group1.genSql('DIAMETER', item);
          const color = group1.genColor(i);
          group1.addSubLayer(props.view, layerId, 'DN' + item, sql, color);
        });
      });
    }
    // .then(() => {
    // props.view?.goTo(pipelayer.fullExtent)
    // })
  }
  const extent = props.view?.extent?.clone();
  extent &&
    homeButton &&
    (homeButton.goToOverride = (view: any) => {
      view.goTo(extent);
    });
  callBack && callBack();
  return {
    homeButton
  };
};
defineExpose({
  init
});
</script>
<style lang="scss" scoped>
.tools-temp-wrapper {
  position: absolute;
}
.custom-toolbar {
  line-height: 32px;
  text-align: center;
  display: flex;

  .tool-icon {
    margin: auto;
  }
}

.overviewmap {
  width: 240px;
  height: 135px;
  position: absolute;
  right: 70px;
  bottom: 15px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.5);
}
</style>
<style lang="scss">
.tool-panel {
  min-width: 260px;
  min-height: 150px;
  position: absolute;
  bottom: 15px;
  right: 70px;
}
</style>
