<template>
  <div class="measure-container">
    <Form ref="refForm" :config="FormConfig"></Form>
  </div>
</template>
<script lang="ts" setup>
import { request } from '@/plugins/axios/geoserver';
import { QueryByPolygon } from '@/utils/geoserver/wfsUtils';
import { useSketch } from '@/hooks/arcgis';
import { useGisStore } from '@/store';
import {
  EStatisticField,
  getGraphicLayer,
  getSubLayerIds
} from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';

const props = defineProps<{
  view?: __esri.MapView;
  telport?: string;
}>();
const state = reactive<{
  measuring: boolean;
  disabled: boolean;
}>({
  measuring: false,
  disabled: true
});
const staticState: {
  sketch?: __esri.SketchViewModel;
  queryGeometry?: __esri.Geometry;
  graphicsLayer?: __esri.GraphicsLayer;
} = {};
const refForm = ref<IFormIns>();
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'select',
          clearable: false,
          options: [],
          label: '管线图层',
          field: 'pipeLayer'
        },
        { type: 'text', field: 'pipeLength', label: '管线长度', unit: '米' },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '新测量',
              type: 'default',
              loading: () => state.measuring,
              disabled: () => state.disabled,
              click: () => startMeasure()
            }
          ]
        }
      ]
    }
  ],
  gutter: 12
});
const { initSketch, destroySketch } = useSketch();

const resolveMeasure = async () => {
  if (!staticState.queryGeometry) return;
  state.measuring = true;

  const pipeLayerId = refForm.value?.dataForm.pipeLayer;
  try {
    // 1. 获取图层名
    const field = FormConfig.group[0].fields[0] as IFormSelect;
    const selected = field.options?.find(opt => opt.value === pipeLayerId);
    const layerName = selected?.data?.layername || pipeLayerId;
    // 2. 查询所有要素
    const res = await QueryByPolygon(layerName, staticState.queryGeometry?.rings[0], '1=1');
    const features = res.data?.features || [];

    // 3. 累加长度字段
    let totalLength = 0;
    for (const f of features) {
      // 假设长度字段为 ShapeLen 或 LENGTH
      const len = Number(f.properties['管长'] || 0);
      totalLength += len;
    }

    refForm.value && (refForm.value.dataForm.pipeLength = totalLength.toFixed(2));
  } catch (error) {
    SLMessage.error('统计失败');
    refForm.value && (refForm.value.dataForm.pipeLength = undefined);
    console.dir(error);
  }
  state.measuring = false;
};
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  if (result.state === 'complete') {
    staticState.queryGeometry = result.graphics[0]?.geometry;
    resolveMeasure();
  }
};
const startMeasure = () => {
  staticState.graphicsLayer?.removeAll();
  staticState.sketch?.create('polygon');
};
const initPipeLayerOption = async () => {
  if (!props.view) return;
  const layerInfo = props.view?.layerViews.items[0].layer.sublayers;
  let layers = layerInfo.items.map(item => {
    return {
      label: item.name,
      value: item.name,
      // data: item
    }
  });
  state.disabled = false;
  const field = FormConfig.group[0].fields[0] as IFormSelect;
  field && (field.options = layers);
  refForm.value &&
    (refForm.value.dataForm.pipeLayer = layers[0]?.value);
};
onMounted(async () => {
  if (!props.view) return;
  initPipeLayerOption();
  staticState.graphicsLayer = getGraphicLayer(props.view, {
    id: 'pipe-length',
    title: '管线长度测量'
  });
  staticState.sketch = initSketch(props.view, staticState.graphicsLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  });
});
onBeforeUnmount(() => {
  destroySketch();
  staticState.graphicsLayer &&
    props.view?.map.remove(staticState.graphicsLayer);
});
</script>
<style lang="scss" scoped>
.measure-container {
  width: 280px;
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
