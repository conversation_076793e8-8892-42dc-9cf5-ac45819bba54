<!-- 管网数据采集系统-数据总览 -->
<template>
  <DrawerBox
    ref="refDrawer"
    :left-drawer="false"
    :right-drawer="false"
    :bottom-drawer="true"
    :left-drawer-absolute="false"
    :bottom-drawer-bar-position="'left'"
    :bottom-drawer-title="'数据总览'"
  >
    <ArcLayout
      @map-loaded="onMapLoaded"
      @pipe-loaded="onPipeLoaded"
    ></ArcLayout>
    
    <template #bottom>
      <div class="data-overview-content">
        <!-- 图表区域 -->
                 <div class="charts-section">
           <!-- 所有图表在同一行 -->
           <div class="all-charts">
             <div class="chart-item">
               <h4>管线长度统计</h4>
               <div id="pipelineLengthChart" class="chart-container"></div>
             </div>
             <div class="chart-item">
               <h4>人员工作量统计</h4>
               <div id="workloadChart" class="chart-container"></div>
             </div>
             <div class="chart-item">
               <h4>已入库资产统计</h4>
               <div id="assetChart" class="chart-container"></div>
             </div>
             <div class="chart-item">
               <h4>已入库工程统计</h4>
               <div id="projectChart" class="chart-container"></div>
             </div>
           </div>
         </div>
        
        <!-- 工程列表区域 -->
        <div class="project-list-section">
          <h4>工程列表</h4>
          <FormTable :config="TableConfigProject"></FormTable>
        </div>
      </div>
    </template>
  </DrawerBox>
</template>

<script lang="ts" setup>
import { onMounted, onBeforeUnmount, reactive, ref } from 'vue'
import * as echarts from 'echarts'
import DrawerBox from '@/components/DrawerBox/DrawerBox.vue'
import ArcLayout from '@/components/arcMap/widgets/ArcLayout.vue'
import FormTable from '@/components/Form/FormTable.vue'
import { formatDate } from '@/utils/DateFormatter'
import { formatterDateTime } from '@/utils/GlobalHelper'
import { 
  getProjectNatureOptions, 
  getDistrictOptions, 
  getAuditStatusOptions,
  getMeasurementUnitOptions 
} from './config'

const refDrawer = ref<any>()

const staticState: {
  view?: __esri.MapView
  charts: { [key: string]: echarts.ECharts }
} = {
  charts: {}
}

// 工程列表表格配置
const TableConfigProject = reactive<ITable>({
  indexVisible: true,
  height: '300px',
  columns: [
    { 
      label: '工程全称', 
      prop: 'fullName',
      minWidth: 200,
      showOverflowTooltip: true
    },
    { 
      label: '工程编号', 
      prop: 'projectCode',
      minWidth: 120
    },
    { 
      label: '工程性质', 
      prop: 'nature',
      minWidth: 100,
      formatter(row, value) {
        const option = getProjectNatureOptions().find(item => item.value === value)
        return option?.label || value
      }
    },
    { 
      label: '所属区域', 
      prop: 'district',
      minWidth: 120,
      formatter(row, value) {
        const option = getDistrictOptions().find(item => item.value === value)
        return option?.label || value
      }
    },
    { 
      label: '审核状态', 
      prop: 'auditStatus',
      minWidth: 100,
      tag: true,
      align: 'center',
      tagColor: row => getAuditStatusTagColor(row.auditStatus),
      formatter(row, value) {
        const option = getAuditStatusOptions().find(item => item.value === value)
        return option?.label || '待审核'
      }
    },
    { 
      label: '审核人', 
      prop: 'auditor',
      minWidth: 100
    },
    { 
      label: '入库状态', 
      prop: 'storageStatus',
      minWidth: 100,
      tag: true,
      align: 'center',
      tagColor: row => getStorageStatusTagColor(row.storageStatus),
      formatter(row, value) {
        return getStorageStatusText(value)
      }
    },
    { 
      label: '测量单位', 
      prop: 'measurementUnit',
      minWidth: 100,
      formatter(row, value) {
        const option = getMeasurementUnitOptions().find(item => item.value === value)
        return option?.label || value
      }
    },
    {
      label: '详情',
      prop: 'actions',
      minWidth: 80,
      align: 'center',
      formItemConfig: {
        type: 'btn-group',
        btns: [
          {
            perm: true,
            text: '详情',
            styles: {
              width: '100%'
            },
            click: row => handleViewProjectDetail(row),
            isTextBtn: true
          }
        ]
      }
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfigProject.pagination.page = page
      TableConfigProject.pagination.limit = size
      refreshProjectData()
    }
  },
  dataList: []
})

/**
 * 获取审核状态标签颜色
 */
const getAuditStatusTagColor = (status: string) => {
  const colorMap: { [key: string]: string } = {
    'pending': '#909399',
    'reviewing': '#E6A23C',
    'approved': '#67C23A',
    'rejected': '#F56C6C',
    'resubmitted': '#409EFF'
  }
  return colorMap[status] || '#909399'
}

/**
 * 获取入库状态标签颜色
 */
const getStorageStatusTagColor = (status: string) => {
  const colorMap: { [key: string]: string } = {
    'not_stored': '#909399',
    'storing': '#E6A23C',
    'stored': '#67C23A',
    'failed': '#F56C6C'
  }
  return colorMap[status] || '#909399'
}

/**
 * 获取入库状态文本
 */
const getStorageStatusText = (status: string) => {
  const textMap: { [key: string]: string } = {
    'not_stored': '未入库',
    'storing': '入库中',
    'stored': '已入库',
    'failed': '入库失败'
  }
  return textMap[status] || '未入库'
}

/**
 * 查看工程详情
 */
const handleViewProjectDetail = (row: any) => {
  console.log('查看工程详情:', row)
  // TODO: 实现工程详情查看逻辑
}

/**
 * 初始化图表
 */
const initCharts = () => {
  initPipelineLengthChart()
  initWorkloadChart()
  initAssetChart()
  initProjectChart()
}

/**
 * 初始化管线长度统计饼状图
 */
const initPipelineLengthChart = () => {
  const chartDom = document.getElementById('pipelineLengthChart')
  if (!chartDom) return

  const myChart = echarts.init(chartDom)
  staticState.charts.pipelineLength = myChart

  const option = {

    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c}km ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '管线长度',
        type: 'pie',
        radius: '60%',
        data: [
          { value: 1048, name: '供水管线' },
          { value: 735, name: '排水管线' },
          { value: 580, name: '雨水管线' },
          { value: 484, name: '污水管线' },
          { value: 300, name: '其他管线' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  myChart.setOption(option)
}

/**
 * 初始化人员工作量统计饼状图
 */
const initWorkloadChart = () => {
  const chartDom = document.getElementById('workloadChart')
  if (!chartDom) return

  const myChart = echarts.init(chartDom)
  staticState.charts.workload = myChart

  const option = {

    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c}工时 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '工作量',
        type: 'pie',
        radius: '60%',
        data: [
          { value: 320, name: '张三' },
          { value: 240, name: '李四' },
          { value: 180, name: '王五' },
          { value: 150, name: '赵六' },
          { value: 110, name: '其他' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  myChart.setOption(option)
}

/**
 * 初始化已入库资产统计柱状图
 */
const initAssetChart = () => {
  const chartDom = document.getElementById('assetChart')
  if (!chartDom) return

  const myChart = echarts.init(chartDom)
  staticState.charts.asset = myChart

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: ['管点', '阀门', '消火栓', '水表', '泵站', '水池'],
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    series: [
      {
        name: '资产数量',
        type: 'bar',
        barWidth: '60%',
        data: [1200, 800, 600, 400, 50, 20],
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  }

  myChart.setOption(option)
}

/**
 * 初始化已入库工程统计柱状图
 */
const initProjectChart = () => {
  const chartDom = document.getElementById('projectChart')
  if (!chartDom) return

  const myChart = echarts.init(chartDom)
  staticState.charts.project = myChart

  const option = {

    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: ['新建工程', '改建工程', '扩建工程', '维修工程', '检测工程', '维护工程'],
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    series: [
      {
        name: '工程数量',
        type: 'bar',
        barWidth: '60%',
        data: [45, 32, 28, 65, 22, 18],
        itemStyle: {
          color: '#67C23A'
        }
      }
    ]
  }

  myChart.setOption(option)
}

/**
 * 刷新工程列表数据
 */
const refreshProjectData = () => {
  // 模拟数据，实际开发中需要调用API
  const mockData = [
    {
      id: 1,
      fullName: '瓜州县供水管网改造工程第一期',
      projectCode: 'GZ2024001',
      nature: 'rebuild',
      district: 'guazhou',
      auditStatus: 'approved',
      auditor: '张三',
      storageStatus: 'stored',
      measurementUnit: 'km'
    },
    {
      id: 2,
      fullName: '锁阳城镇污水管网新建工程',
      projectCode: 'GZ2024002',
      nature: 'new',
      district: 'suoyangcheng',
      auditStatus: 'reviewing',
      auditor: '李四',
      storageStatus: 'storing',
      measurementUnit: 'm'
    },
    {
      id: 3,
      fullName: '双塔乡供水设施维护工程',
      projectCode: 'GZ2024003',
      nature: 'maintenance',
      district: 'shuangta',
      auditStatus: 'pending',
      auditor: '',
      storageStatus: 'not_stored',
      measurementUnit: 'm'
    },
    {
      id: 4,
      fullName: '布隆吉乡管网检测工程',
      projectCode: 'GZ2024004',
      nature: 'inspection',
      district: 'bulongji',
      auditStatus: 'approved',
      auditor: '王五',
      storageStatus: 'stored',
      measurementUnit: 'km'
    },
    {
      id: 5,
      fullName: '梁湖乡供水管线扩建工程',
      projectCode: 'GZ2024005',
      nature: 'expand',
      district: 'lianghu',
      auditStatus: 'rejected',
      auditor: '赵六',
      storageStatus: 'failed',
      measurementUnit: 'km'
    }
  ]

  TableConfigProject.dataList = mockData
  TableConfigProject.pagination.total = mockData.length
}

/**
 * 调整图表大小
 */
const resizeCharts = () => {
  Object.values(staticState.charts).forEach(chart => {
    chart.resize()
  })
}

const onMapLoaded = async (view: __esri.MapView) => {
  staticState.view = view
  refreshProjectData()
}

const onPipeLoaded = () => {
  // 打开底部抽屉
  refDrawer.value?.toggleDrawer('btt', true)
  
  // 延迟初始化图表，确保DOM已渲染
  setTimeout(() => {
    initCharts()
    // 监听窗口大小变化
    window.addEventListener('resize', resizeCharts)
  }, 500)
}

onMounted(() => {
  // 页面挂载时不需要特殊操作，等待地图加载完成
})

onBeforeUnmount(() => {
  // 销毁图表实例
  Object.values(staticState.charts).forEach(chart => {
    chart.dispose()
  })
  // 移除事件监听
  window.removeEventListener('resize', resizeCharts)
})
</script>

<style lang="scss" scoped>
.data-overview-content {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  
  .charts-section {
    margin-bottom: 30px;
    
    .all-charts {
      display: flex;
      gap: 15px;
      
      .chart-item {
        flex: 1;
        
        h4 {
          margin: 0 0 10px 0;
          font-size: 14px;
          font-weight: 600;
          color: #333;
          text-align: center;
        }
        
        .chart-container {
          width: 100%;
          height: 280px;
        }
      }
    }
  }
  
  .project-list-section {
    h4 {
      margin: 0 0 15px 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
      border-bottom: 2px solid #409EFF;
      padding-bottom: 8px;
    }
  }
}

@media (max-width: 1200px) {
  .data-overview-content {
    .charts-section {
      .all-charts {
        flex-wrap: wrap;
        
        .chart-item {
          flex: 0 0 48%;
          
          .chart-container {
            height: 250px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .data-overview-content {
    .charts-section {
      .all-charts {
        .chart-item {
          flex: 0 0 100%;
          
          .chart-container {
            height: 220px;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.gis-bottom-detail-drawer {
  width: 100%;
  height: 50vh;
  position: absolute;
  bottom: 0;
  left: 0;
}

// 只针对当前页面的下侧抽屉高度设置
.layout-drawerbox-container {
  .layout-drawer-btt {
    height: 600px !important;
    
    // 修复收起时的负边距，确保抽屉能正常隐藏
    &.collapsed {
      margin-bottom: -600px !important;
    }
  }
}
</style> 