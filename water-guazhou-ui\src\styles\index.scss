@use './variables.scss' as *;
@use './mixin.scss' as *;
@use './transition.scss' as *;
@use './fonts.scss' as *;
@use './theme/dark/index.scss' as dark;
@use './theme/light/index.scss' as light;
* {
  @include scrollBar;

}

/** overlay */
.overlay {
  @include overlay;
  @include scrollBar;
}

.overlay-y {
  @include overlay-y;
  @include scrollBar;
}
.overlay-x {
  @include overlay-x;
  @include scrollBar;
}

body {
  height: 100%;
  overflow: hidden;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  // font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;

  .main-container {
    // min-height: 100%;
    transition: margin-left .28s;
    // margin-left: 180px;
    flex: 1;
    position: relative;
  }
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  outline: none;
  text-decoration: none;
}

div:focus {
  outline: none;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

//main-container全局样式
.app-main {
  height: calc(100vh - 60px);
  // min-height: 100%
}

.app-container {
  padding: 20px;
}

.location-label {
  color: red;
}

.color-block {
  width: 2px;
  height: 16px;
  background: #2a96d5;
}

.tree-right-detail-box {
  height: 100%;
}

.wrapper {
  height: 100%;
  padding: 15px;
}

.map-wrapper {
  height: 100%;
  width: 100%;
  position: relative;
}

.card-table {
  height: calc(100% - 75px);
}

.titel-left {
  width: 100px;
  display: flex;
  align-items: center;

  div {
    margin: 10px;
  }
}

.template-protocol-container {

  /*************按钮***************************/
  // 按钮自定义颜色
  .query-yellow {
    color: $color-white;
    border-color: #f7a115;
    background-color: #f7a115;

    &:hover,
    &:active,
    &:focus {
      color: $color-white;
      background-color: #ffb800;
    }
  }

  .add-purple {
    color: $color-white;
    border-color: #c227c1;
    background-color: #c227c1;

    &:hover,
    &:active,
    &:focus {
      color: $color-white;
      border-color: #c227c1;
      background-color: #c227c1;
      // background-color: rgb(3, 179, 0);
    }
  }

  .reset-green {
    color: $color-white;
    border-color: #0bbc07;
    background-color: #0bbc07;

    &:hover,
    &:active,
    &:focus {
      color: $color-white;
      background-color: rgb(3, 179, 0);
    }
  }

  .reset-green2 {
    color: $color-white;
    border-color: #35ac90;
    background-color: #35ac90;

    &:hover,
    &:active,
    &:focus {
      color: $color-white;
      background-color: #0aad87;
    }
  }

  .add-child-blue {
    color: $color-white;
    border-color: #40b5e6;
    background-color: #40b5e6;

    &:hover,
    &:active,
    &:focus {
      color: $color-white;
      background-color: #32d1db;
    }
  }

  .btn-blue-color1 {
    color: $color-white;
    border-color: #3d94dd;
    background-color: #3d94dd;

    &:hover,
    &:active,
    &:focus {
      color: $color-white;
      background-color: #1782da;
    }
  }

  .edit-primary-blue {
    color: $color-white;
    border-color: #3897ff;
    background-color: #3897ff;

    &:hover,
    &:active,
    &:focus {
      color: $color-white;
      background-color: #1f9fff;
    }
  }

  .click-chose-type {
    color: $color-white;
    border-color: rgb(143, 148, 253);
    background-color: rgb(143, 148, 253);

    &:hover {
      color: $color-white;
      background-color: rgb(175, 179, 252);
    }

    &:focus {
      color: $color-white;
      background-color: rgb(175, 179, 252);
    }
  }

  .add-blue-green {
    color: $color-white;
    border-color: #33ab9f;
    background-color: #33ab9f;

    &:hover,
    &:active,
    &:focus {
      color: $color-white;
      background-color: rgb(33, 156, 144);
    }
  }

  .delete-orange {
    color: $color-white;
    border-color: #ff5722;
    background-color: #ff5722;

    &:hover,
    &:active,
    &:focus {
      color: $color-white;
      background-color: rgb(245, 75, 23);
    }
  }

  .delete-red {
    color: $color-white;
    border-color: #ff3838;
    background-color: #ff3838;

    &:hover,
    &:active,
    &:focus {
      color: $color-white;
      background-color: #ff5722;
    }
  }

  .info-rose-red {
    color: $color-white;
    border-color: #fa42c3;
    background-color: #fa42c3;

    &:hover,
    &:active,
    &:focus {
      color: $color-white;
      background-color: rgb(250, 50, 190);
    }
  }

  .info-rose-view {
    color: $color-white;
    border-color: #7998ff;
    background-color: #7998ff;

    &:hover,
    &:active,
    &:focus {
      color: $color-white;
      background-color: rgb(113, 90, 241);
    }
  }
}

.gis-bottom-drawer {
  width: 100%;
  height: 300px;
  left: 0;
  bottom: 0;
}

.gis-detail-panel {
  width: 100%;
  height: 350px;
  position: absolute;
  bottom: 0;
  left: 0;
}

.sql-btns-wrapper,
.sql-list-wrapper {
  box-shadow: 0 0 0 1px var(--el-border-color);
}
.iconfont {
  &::before {
    font-size: 1em;
  }
}

.absolute{
  position: absolute;
}
.relative{
  position: relative;
}