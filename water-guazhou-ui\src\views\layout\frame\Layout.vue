<template>
  <div class="app-wrapper" :class="classObj">
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    ></div>
    <AppHeader
      @menu-click="(isCurrentApp?: boolean) => openSideBar(isCurrentApp)"
    ></AppHeader>
    <div class="main-layout">
      <!-- <Sidebar v-if="!alarmView"></Sidebar> -->
      <AppSideBar v-if="appStore.menuType === 'left'" class="app-sidebar"></AppSideBar>
      <AppHeadBar v-if="appStore.menuType === 'top'"></AppHeadBar>
      <div class="main-container" :class="{ autowidth: state.isMoveAlarm }">
        <AppTags v-if="alarmView && tagsStore.showTags"></AppTags>
        <section id="app-main" class="app-main">
          <LayoutFinal></LayoutFinal>
        </section>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// import Sidebar from './components/Sidebar/index.vue';
import { useResize } from '@/hooks/resizeSetup';
import LayoutFinal from './components/LayoutFinal.vue';
import { useAppStore, useTagsStore, useUserStore } from '@/store';
import AppHeader from './AppHeader/index.vue';
import AppSideBar from './AppSideBar/index.vue';
import { hasPermission } from '@/utils/RouterHelper';
import AppHeadBar from './AppHeadBar/index.vue';
import AppTags from './AppTags/index.vue';

const appStore = useAppStore();
const tagsStore = useTagsStore();
const userStore = useUserStore();
const router = useRouter();
const { sidebar, device } = useResize();
const state = reactive<{
  isMoveAlarm: boolean;
}>({
  isMoveAlarm:
    router.currentRoute.value.path === '/moverealTimeAlarm' ||
    router.currentRoute.value.path === '/movehistoryAlarm'
});
const classObj = computed(() => {
  return {
    hideSidebar: !sidebar.value.opened,
    openSidebar: sidebar.value.opened,
    withoutAnimation: sidebar.value.withoutAnimation,
    mobile: device.value === 'mobile',
    hasTags: tagsStore.showTags
  };
});
const alarmView = computed(() => !hasPermission(['SYS_ADMIN']));
const handleClickOutside = () => {
  appStore.CloseSideBar({ withoutAnimation: false });
};
const openSideBar = (isCurrentApp?: boolean) => {
  appStore.TOGGLE_menuShow(isCurrentApp === true ? undefined : true);
};
onBeforeMount(() => {
  const role = userStore.roles[0];
  if (role === 'SYS_ADMIN') {
    // 当前为系统管理员时，且路由为/则跳转到企业管理
    router.currentRoute.value.path === '/' &&
      router.replace({ path: '/tenant/sysTenant' });
  }
});
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@use '@/styles/mixin.scss' as *;
.dark {
  &.app-wrapper {
    background-color: #131624;
  }

  .app-main {
    background-color: #191c27;
  }

  .main-container {
    background-color: #222536;
    border-color: #303340;
    border-top: none;
    margin-top: -1px;
    border-radius: 0 0 14px 14px;

    &:hover {
      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.35), 0 4px 12px rgba(0, 0, 0, 0.25);
    }
  }
}

.main-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
  gap: 8px;
  height: 100%;
}

.app-sidebar {
  width: 200px;
  height: 100%;
  background-color: #fff;
  border-radius: 12px;
  // box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.main-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
  min-height: 0;
  overflow: hidden;
}

.app-main {
  flex: 1;
  min-height: 0;
  width: 100%;
  position: relative;
  overflow: hidden;
  background-color: #fff !important;
}

.app-wrapper {
  position: relative;
  display: flex;
  height: 100%;
  width: 100%;
  flex-direction: column;
  background-color: #f3f3f3;
  @include clearfix;
  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
  .main-container {
    background-color: #fff;
    // border-radius: 12px;
    // box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    overflow: hidden;
    // border: 1px solid #e4e7ed;
    // border-top: none;
    position: relative;
    z-index: 5;
    margin-top: -1px;
    flex: 1;
  }

  .autowidth {
    min-width: auto;
  }
  &.hasTags {
    .main-layout {
      height: calc(100% - 88px);
    }
  }
}
</style>
