import { request } from '@/plugins/axios/geoserver';

/**
 * 管线分析工具类
 * 基于实际的gisAnalyse接口实现
 */

/**
 * 沿线分析
 * @param {Object} params - 查询参数
 * @param {string|number} params.startId - 起点节点ID
 * @param {string|number} params.endId - 终点节点ID
 * @param {number} [params.pageNum] - 页码（可选）
 * @param {number} [params.pageSize] - 每页数量（可选）
 * @returns {Promise<any>} 沿线分析结果
 */
export const alongAnalysis = async (params) => {
  try {
    const response = await request({
      url: '/gisAnalyse/along',
      method: 'GET',
      params: {
        layer: params.layer,
        startId: params.startId,
        endId: params.endId,
        pageNum: params.pageNum,
        pageSize: params.pageSize
      }
    });
    return response.data;
  } catch (error) {
    console.error('沿线分析失败:', error);
    throw error;
  }
};

/**
 * 缓冲区分析
 * @param {Object} params - 请求参数
 * @param {string} params.layerName - 图层名称
 * @param {Object} params.geometry - 几何对象
 * @param {number} params.distance - 缓冲区距离（米）
 * @param {string} params.where - 查询条件
 * @returns {Promise} 缓冲区分析结果
 */
export const bufferAnalysis = async (params) => {
  try {
    const response = await request({
      url: '/gisAnalyse/buffer',
      method: 'POST',
      data: {
        layerName: params.layerName,
        geometry: params.geometry,
        distance: params.distance,
        where: params.where || '1=1'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('缓冲区分析失败:', error);
    throw error;
  }
};

/**
 * 连通性分析
 * @param {Object} params - 查询参数
 * @param {string} params.layerName - 管线图层名称
 * @param {string} params.nodeLayerName - 节点图层名称
 * @param {Object} params.startPoint - 起始点
 * @param {Object} params.endPoint - 终点
 * @param {string} params.where - 查询条件
 * @returns {Promise} 连通性分析结果
 */
export const connectedAnalysis = async (params) => {
  try {
    const response = await request({
      url: '/gisAnalyse/connected',
      method: 'GET',
      params: {
        layerName: params.layerName,
        nodeLayerName: params.nodeLayerName,
        startPoint: JSON.stringify(params.startPoint),
        endPoint: JSON.stringify(params.endPoint),
        where: params.where || '1=1'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('连通性分析失败:', error);
    throw error;
  }
};

/**
 * 截图分析
 * @param {Object} params - 查询参数
 * @param {Object} params.extent - 截图范围
 * @param {string} params.format - 图片格式 (png, jpg, etc.)
 * @param {number} params.width - 图片宽度
 * @param {number} params.height - 图片高度
 * @returns {Promise} 截图结果
 */
export const screenshotAnalysis = async (params) => {
  try {
    const response = await request({
      url: '/gisAnalyse/screenshot',
      method: 'GET',
      params: {
        extent: JSON.stringify(params.extent),
        format: params.format || 'png',
        width: params.width || 800,
        height: params.height || 600
      },
      responseType: 'blob' // 返回图片数据
    });
    
    return response.data;
  } catch (error) {
    console.error('截图分析失败:', error);
    throw error;
  }
};

/**
 * 爆管分析
 * @param {Object} params - 请求参数
 * @param {string} params.layerName - 管线图层名称
 * @param {Object} params.incidentPoint - 爆管点
 * @param {number} params.radius - 影响半径（米）
 * @param {string} params.where - 查询条件
 * @returns {Promise} 爆管分析结果
 */
export const burstpipeAnalysis = async (params) => {
  try {
    const response = await request({
      url: '/gisAnalyse/burstpipe',
      method: 'POST',
      data: {
        layerName: params.layerName,
        incidentPoint: params.incidentPoint,
        radius: params.radius,
        where: params.where || '1=1'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('爆管分析失败:', error);
    throw error;
  }
};

/**
 * 管线网络分析工具类
 */
export class PipelineNetworkAnalyzer {
  constructor(layerName, nodeLayerName = null) {
    this.layerName = layerName;
    this.nodeLayerName = nodeLayerName;
  }

  /**
   * 分析管线连通性
   * @param {Object} startPoint - 起始点
   * @param {Object} endPoint - 终点
   * @param {string} where - 查询条件
   * @returns {Promise} 连通性分析结果
   */
  async analyzeConnectivity(startPoint, endPoint, where = '1=1') {
    return await connectedAnalysis({
      layerName: this.layerName,
      nodeLayerName: this.nodeLayerName,
      startPoint,
      endPoint,
      where
    });
  }

  /**
   * 沿线分析
   * @param {Object} geometry - 几何对象
   * @param {number} distance - 沿线距离
   * @param {string} where - 查询条件
   * @returns {Promise} 沿线分析结果
   */
  async analyzeAlong(geometry, distance, where = '1=1') {
    return await alongAnalysis({
      layerName: this.layerName,
      geometry,
      distance,
      where
    });
  }

  /**
   * 缓冲区分析
   * @param {Object} geometry - 几何对象
   * @param {number} distance - 缓冲区距离
   * @param {string} where - 查询条件
   * @returns {Promise} 缓冲区分析结果
   */
  async analyzeBuffer(geometry, distance, where = '1=1') {
    return await bufferAnalysis({
      layerName: this.layerName,
      geometry,
      distance,
      where
    });
  }
}

/**
 * 管线应急分析工具类
 */
export class PipelineEmergencyAnalyzer {
  constructor(layerName) {
    this.layerName = layerName;
  }

  /**
   * 爆管影响分析
   * @param {Object} incidentPoint - 爆管点
   * @param {number} radius - 影响半径
   * @param {string} where - 查询条件
   * @returns {Promise} 爆管分析结果
   */
  async analyzeBurstpipe(incidentPoint, radius, where = '1=1') {
    return await burstpipeAnalysis({
      layerName: this.layerName,
      incidentPoint,
      radius,
      where
    });
  }

  /**
   * 影响范围截图
   * @param {Object} extent - 截图范围
   * @param {string} format - 图片格式
   * @param {number} width - 图片宽度
   * @param {number} height - 图片高度
   * @returns {Promise} 截图结果
   */
  async takeScreenshot(extent, format = 'png', width = 800, height = 600) {
    return await screenshotAnalysis({
      extent,
      format,
      width,
      height
    });
  }
}

/**
 * 管线空间分析工具类
 */
export class PipelineSpatialAnalyzer {
  constructor(layerName) {
    this.layerName = layerName;
  }

  /**
   * 缓冲区分析
   * @param {Object} geometry - 几何对象
   * @param {number} distance - 缓冲区距离
   * @param {string} where - 查询条件
   * @returns {Promise} 缓冲区分析结果
   */
  async bufferAnalysis(geometry, distance, where = '1=1') {
    return await bufferAnalysis({
      layerName: this.layerName,
      geometry,
      distance,
      where
    });
  }

  /**
   * 沿线分析
   * @param {Object} geometry - 几何对象
   * @param {number} distance - 沿线距离
   * @param {string} where - 查询条件
   * @returns {Promise} 沿线分析结果
   */
  async alongAnalysis(geometry, distance, where = '1=1') {
    return await alongAnalysis({
      layerName: this.layerName,
      geometry,
      distance,
      where
    });
  }
}

/**
 * 便捷函数 - 缓冲区分析
 * @param {string} layerName - 图层名称
 * @param {Object} geometry - 几何对象
 * @param {number} distance - 缓冲区距离（米）
 * @param {string} where - 查询条件
 * @returns {Promise} 缓冲区分析结果
 */
export const buffer = async (layerName, geometry, distance, where = '1=1') => {
  return await bufferAnalysis({ layerName, geometry, distance, where });
};

/**
 * 便捷函数 - 沿线分析
 * @param {string} layerName - 图层名称
 * @param {Object} geometry - 几何对象
 * @param {number} distance - 沿线距离（米）
 * @param {string} where - 查询条件
 * @returns {Promise} 沿线分析结果
 */
export const along = async (layerName, geometry, distance, where = '1=1') => {
  return await alongAnalysis({ layerName, geometry, distance, where });
};

/**
 * 便捷函数 - 连通性分析
 * @param {string} layerName - 管线图层名称
 * @param {string} nodeLayerName - 节点图层名称
 * @param {Object} startPoint - 起始点
 * @param {Object} endPoint - 终点
 * @param {string} where - 查询条件
 * @returns {Promise} 连通性分析结果
 */
export const connected = async (layerName, nodeLayerName, startPoint, endPoint, where = '1=1') => {
  return await connectedAnalysis({ layerName, nodeLayerName, startPoint, endPoint, where });
};

/**
 * 便捷函数 - 爆管分析
 * @param {string} layerName - 管线图层名称
 * @param {Object} incidentPoint - 爆管点
 * @param {number} radius - 影响半径（米）
 * @param {string} where - 查询条件
 * @returns {Promise} 爆管分析结果
 */
export const burstpipe = async (layerName, incidentPoint, radius, where = '1=1') => {
  return await burstpipeAnalysis({ layerName, incidentPoint, radius, where });
};

/**
 * 便捷函数 - 截图
 * @param {Object} extent - 截图范围
 * @param {string} format - 图片格式
 * @param {number} width - 图片宽度
 * @param {number} height - 图片高度
 * @returns {Promise} 截图结果
 */
export const screenshot = async (extent, format = 'png', width = 800, height = 600) => {
  return await screenshotAnalysis({ extent, format, width, height });
};

export default {
  // 核心分析函数
  alongAnalysis,
  bufferAnalysis,
  connectedAnalysis,
  screenshotAnalysis,
  burstpipeAnalysis,
  
  // 便捷函数
  along,
  buffer,
  connected,
  burstpipe,
  screenshot,
  
  // 工具类
  PipelineNetworkAnalyzer,
  PipelineEmergencyAnalyzer,
  PipelineSpatialAnalyzer
};