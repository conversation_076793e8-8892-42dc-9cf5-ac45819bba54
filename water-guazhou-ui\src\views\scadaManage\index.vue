<!-- 组态首页 -->
<template>
  <!-- 组态列表 -->
  <TreeBox>
    <template #tree>
      <TreeList :tree-data="treeData" />
    </template>
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      title="组态列表"
      class="card-table"
      overlay
    >
      <div class="scadaContainer">
        <ScadaItem
          v-for="item in scadaList"
          :key="item.id"
          :scada="item"
          @set="setScada"
          @copy="copyScada"
          @del="delScada"
        />
      </div>
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[5, 10, 20, 50]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </SLCard>

    <AddOrUpdateDialog
      v-if="addOrUpdateConfig.visible"
      ref="addOrUpdate"
      :config="addOrUpdateConfig"
      @refreshData="refreshData"
    />
  </TreeBox>
</template>

<script>
import moment from 'moment'
import { v4 as uuidv4 } from 'uuid'
import { shallowRef } from 'vue'
import { Search, Plus } from '@element-plus/icons-vue'
import ScadaItem from './components/scadaItem.vue'
import { getFindScadaList, deleteScada } from '@/api/scada'
import { getProjectRoot } from '@/api/project'
import { removeSlash } from '@/utils/removeIdSlash'
import useGlobal from '@/hooks/global/useGlobal'
import TreeBox from '../layout/treeOrDetailFrame/TreeBox.vue'
import { useUserStore } from '@/store'

const { $message } = useGlobal()

export default {
  name: 'Scada',
  components: { ScadaItem, TreeBox },
  setup() {
    return {
      Search,
      Plus
    }
  },
  data() {
    return {
      totalLoading: false,
      scadaList: [],
      detailContainer: false,
      tenantId: '',
      // previewDialog: {
      //   visible: false,
      //   url: ''
      // },
      cardSearchConfig: {
        filters: [
          { label: '搜索', field: 'name', type: 'input' },
          {
            type: 'btn-group',
            btns: [
              {
                perm: true,
                text: '查询',
                svgIcon: shallowRef(Search),
                click: () => this.refreshData()
              },
              {
                text: '添加组态',
                perm: true,
                svgIcon: shallowRef(Plus),
                click: () => {
                  this.addOrUpdateConfig.defaultValue = {
                    originatorId: this.treeData.currentProject.id,
                    protect: 0,
                    width: 800,
                    height: 600,
                    protectPwd: '',
                    tenantId: this.tenantId,
                    chartType: 'cloud'
                  }
                  this.addOrUpdateConfig.title = '添加组态'
                  this.addOrUpdateConfig.visible = true
                }
              }
            ]
          }
        ]
      },
      addOrUpdateConfig: {
        visible: false,
        title: '添加设备',
        close: () => {
          this.addOrUpdateConfig.visible = false
        },
        addUrl: '/api/zutai/dashboard-list',
        editUrl: '/api/zutai/dashboard-list',
        defaultValue: {},
        externalParams: {},
        setSubmitParams: params => {
          console.log(params)
          if (!params.dashboardId) {
            params.dashboardId = uuidv4()
          }

          if (!params.dashboardId) params.dashboardId = uuidv4()
          params.dashboardId = params.dashboardId.replace(/-/g, '')
          const data = '{"pageConfig":{"width":'
            + params.width
            + ',"height":'
            + params.height
            + ',"backgroundColor":"#ffffff"},"assets":[],"panels":[]}'
          if (params._id) {
            return {
              ...params,
              data,
              _id: params.id
            }
          }
          return {
            ...params,
            protect: false,
            protectPwd: '',
            checked: false,
            data,
            // data: '{"pageConfig":{"width":800,"height":600,"backgroundColor":"#ffffff"},"assets":[],"panels":[]}',
            projectId: this.treeData.currentProject.id
          }
        },
        height: '300px',
        columns: [
          {
            type: 'input',
            label: '组态名称',
            key: 'name',
            rules: [{ required: true, message: '请填写组态名称' }]
          },
          // {
          //   type: 'input',
          //   label: '组态宽度',
          //   key: 'width'
          // },
          // {
          //   type: 'input',
          //   label: '组态高度',
          //   key: 'height'
          // },
          {
            type: 'textarea',
            label: '备注',
            key: 'detail'
          }
        ]
      },
      treeData: {
        that: this,
        title: '区域划分',
        data: [],
        loading: false,
        isFilterTree: true,
        currentId: '',
        currentProject: {},
        expandNodeId: [],
        defaultProps: {
          children: 'children',
          label: 'name'
        },
        treeNodeHandleClick: data => {
          // 设置当前选中项目信息
          this.treeData.currentProject = data
          this.refreshData()
        }
      },
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  created() {
    this.tenantId = removeSlash(useUserStore().tenantId)
    this.refreshTree(true)
  },

  methods: {
    async refreshData(isFirst) {
      this.detailContainer = true
      const paramsObj = {
        page: this.pagination.currentPage,
        size: this.pagination.pageSize,
        projectId: this.treeData.currentProject.id
      }
      if (!isFirst) Object.assign(paramsObj, this.$refs.cardSearch.queryParams)
      try {
        const res = await getFindScadaList(paramsObj)
        this.detailContainer = false
        if (res.status === 200) {
          this.scadaList = []
          // for (const val of res.data) {
          //   val.keyId = val.id
          //   val.time = moment(val.createTime).format('YYYY/MM/DD HH:mm:ss')
          //   if (val.chartType !== 'local') {
          //     this.scadaList.push(val)
          //   }
          // }
          this.pagination.total = res.data.total || 0

          // 添加数据验证
          if (res.data.data && Array.isArray(res.data.data)) {
            this.scadaList = res.data.data.filter(item => item && item.id).map(item => {
              item.keyId = item.id
              item.time = moment(item.createTime).format('YYYY/MM/DD HH:mm:ss')
              return item
            })
          } else if (res.data && Array.isArray(res.data)) {
            // 直接返回数组的情况
            this.scadaList = res.data.filter(item => item && item.id).map(item => {
              item.keyId = item.id
              item.time = moment(item.createTime).format('YYYY/MM/DD HH:mm:ss')
              return item
            })
          } else {
            console.warn('组态数据格式异常:', res.data)
            this.scadaList = []
          }
        } else {
          $message.error('获取失败')
        }
      } catch (error) {
        console.log(error)
        this.detailContainer = false
      }
    },
    refreshTree(isFirst) {
      getProjectRoot()
        .then(res => {
          if (res.data) {
            this.treeData.data = res.data
            const fTData = this.treeData.data.filter(v => !v.disabled)
            this.treeData.currentProject = fTData[0]
            this.totalLoading = false
            this.refreshData(isFirst)
          } else {
            $message('暂无项目 不可操作，请创建项目')
            this.totalLoading = false
          }
        })
        .catch(err => {
          console.log(err)
          $message('暂无项目 不可操作，请创建项目')
          this.totalLoading = false
        })
    },

    setScada(scada) {
      let data = {}
      if (scada.data) {
        try {
          data = JSON.parse(scada.data)
        } catch (error) {
          console.log(error)
        }
      }

      const defaultValue = {
        ...scada,
        width: data.pageConfig ? data.pageConfig.width : 800,
        height: data.pageConfig ? data.pageConfig.height : 600
      }
      this.addOrUpdateConfig.defaultValue = defaultValue
      this.addOrUpdateConfig.visible = true
    },

    copyScada(scadaId) {
      console.log(scadaId)
    },
    delScada(id) {
      deleteScada(id).then(() => {
        $message({
          type: 'success',
          message: '删除成功'
        })
        this.refreshData()
      })
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.refreshData()
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.refreshData()
    }
  }
}
</script>

<style lang="scss" scoped>
.card-search {
  margin: 0;
  margin-bottom: 15px;
}

.detailContainer {
  width: 100%;
  height: calc(100% - 95px);
  flex: 1;
  background-color: #222536;
  overflow-y: auto;
  padding: 16px;

  .title {
    height: 25px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #fff;
    line-height: 25px;
    margin: 0 0 24px 0;
  }
}

.scadaContainer {
  display: flex;
  flex-wrap: wrap;
  height: calc(100% - 40px);
  overflow-y: auto;
}
</style>
