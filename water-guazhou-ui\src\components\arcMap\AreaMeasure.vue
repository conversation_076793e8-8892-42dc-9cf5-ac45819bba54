<template>
  <div class="measure-container">
    <Form ref="refForm" :config="FormConfig"></Form>
  </div>
</template>

<script lang="ts" setup>
import { calcArea, getGraphicLayer } from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';
import { useSketch } from '@/hooks/arcgis';
import * as geometryEngine from '@arcgis/core/geometry/geometryEngine.js';
import * as webMercatorUtils from '@arcgis/core/geometry/support/webMercatorUtils.js';

const props = defineProps<{
  view?: __esri.MapView;
  telport?: string;
}>();

const state = reactive<{
  measuring: boolean;
  measureType: 'distance' | 'rectangle' | 'polygon';
  results: {
    distance?: number;
    perimeter?: number;
    area?: number;
  };
}>({
  measuring: false,
  measureType: 'distance',
  results: {}
});

const staticState: {
  sketch?: __esri.SketchViewModel;
  graphicsLayer?: __esri.GraphicsLayer;
} = {};

const refForm = ref<IFormIns>();

const FormConfig = reactive<IFormConfig>({
  labelWidth: 50,
  group: [
    {
      fieldset: {
        desc: '测量工具'
      },
      fields: [
        {
          type: 'btn-group',
          label: '',
          btns: [
            {
              perm: true,
              title: '直线距离',
              type: () =>
                state.measureType === 'distance' ? 'primary' : 'default',
              iconifyIcon: 'mdi:ruler',
              click: () => setMeasureType('distance'),
              styles: {
                flex: '1',
                minWidth: '0'
              }
            },
            {
              perm: true,
              title: '矩形测量',
              type: () =>
                state.measureType === 'rectangle' ? 'primary' : 'default',
              iconifyIcon: 'mdi:crop',
              click: () => setMeasureType('rectangle'),
              styles: {
                flex: '1',
                minWidth: '0'
              }
            },
            {
              perm: true,
              title: '多边形测量',
              type: () =>
                state.measureType === 'polygon' ? 'primary' : 'default',
              iconifyIcon: 'mdi:vector-polygon',
              click: () => setMeasureType('polygon'),
              styles: {
                flex: '1',
                minWidth: '0'
              }
            },
            {
              perm: true,
              type: 'default',
              title: '清除图形',
              iconifyIcon: 'ep:delete',
              click: () => clearMeasure(),
              styles: {
                flex: '1',
                minWidth: '0'
              }
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '测量结果'
      },
      fields: [
        {
          type: 'text',
          field: 'distance',
          label: '距离',
          readonly: true,
          labelWidth: '40px',

          extraFormItem: [
            {
              type: 'select',
              field: 'distanceUnit',
              options: [
                { label: '米', value: 'meters' },
                { label: '千米', value: 'kilometers' }
              ],
              onChange: () => updateResults()
            }
          ]
        },
        {
          type: 'text',
          field: 'perimeter',
          label: '周长',
          readonly: true,
          labelWidth: '40px',

          extraFormItem: [
            {
              type: 'select',
              field: 'perimeterUnit',
              options: [
                { label: '米', value: 'meters' },
                { label: '千米', value: 'kilometers' }
              ],
              onChange: () => updateResults()
            }
          ]
        },
        {
          type: 'text',
          field: 'area',
          label: '面积',
          readonly: true,
          labelWidth: '40px',

          extraFormItem: [
            {
              type: 'select',
              field: 'areaUnit',
              options: [
                { label: '平方米', value: 'square-meters' },
                { label: '平方千米', value: 'square-kilometers' },
                { label: '公顷', value: 'hectares' }
              ],
              onChange: () => updateResults()
            }
          ]
        }
      ]
    }
  ],
  gutter: 16,
  defaultValue: {
    distanceUnit: 'meters',
    perimeterUnit: 'meters',
    areaUnit: 'square-meters'
  }
});

const { initSketch, destroySketch } = useSketch();

// 设置测量类型并自动开始测量
const setMeasureType = (type: 'distance' | 'rectangle' | 'polygon') => {
  if (state.measuring) {
    // 如果正在测量，先停止当前测量
    staticState.sketch?.cancel();
    state.measuring = false;
  }

  state.measureType = type;
  state.results = {};
  clearFormResults();

  console.log('切换测量类型:', type);

  // 自动开始新的测量
  startMeasure();
};

// 开始测量（内部方法）
const startMeasure = () => {
  if (!staticState.sketch) {
    SLMessage.error('测量工具未初始化');
    return;
  }

  state.measuring = true;
  state.results = {};
  clearFormResults();
  staticState.graphicsLayer?.removeAll();

  // 根据测量类型创建不同的几何图形
  switch (state.measureType) {
    case 'distance':
      staticState.sketch.create('polyline');
      break;
    case 'rectangle':
      staticState.sketch.create('rectangle');
      break;
    case 'polygon':
      staticState.sketch.create('polygon');
      break;
  }

  console.log('开始测量:', state.measureType);
};

// 清除测量
const clearMeasure = () => {
  // 如果正在测量，先取消
  if (state.measuring) {
    staticState.sketch?.cancel();
  }

  staticState.graphicsLayer?.removeAll();
  state.measuring = false;
  state.results = {};
  clearFormResults();

  console.log('清除测量结果');
  SLMessage.success('已清除测量结果');

  // 清除后自动开始新的测量
  setTimeout(() => {
    startMeasure();
  }, 200);
};
// 清除表单结果
const clearFormResults = () => {
  if (refForm.value?.dataForm) {
    refForm.value.dataForm.distance = '';
    refForm.value.dataForm.perimeter = '';
    refForm.value.dataForm.area = '';
  }
};

// 处理绘制完成
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  if (result.state === 'complete' && result.graphics[0]) {
    const geometry = result.graphics[0].geometry;
    calculateMeasurements(geometry);
    state.measuring = false;
  }
};

// 计算测量结果
const calculateMeasurements = (geometry: __esri.Geometry) => {
  try {
    console.log('开始计算测量结果:', {
      measureType: state.measureType,
      geometryType: geometry.type,
      geometry: geometry
    });

    // 确保几何图形在Web Mercator坐标系中
    const webMercatorGeometry =
      webMercatorUtils.geographicToWebMercator(geometry) || geometry;

    console.log('坐标转换后的几何图形:', webMercatorGeometry);

    switch (state.measureType) {
      case 'distance':
        if (geometry.type === 'polyline') {
          const length = geometryEngine.planarLength(
            webMercatorGeometry,
            'meters'
          );
          state.results.distance = length;
          console.log('距离测量结果:', length, '米');
        } else {
          console.warn('距离测量但几何类型不是polyline:', geometry.type);
        }
        break;

      case 'rectangle':
      case 'polygon':
        if (geometry.type === 'polygon') {
          console.log('开始计算多边形面积和周长...');

          // 尝试多种面积计算方法
          let area = 0;
          let perimeter = 0;

          try {
            // 方法1：使用geometryEngine.planarArea
            area = geometryEngine.planarArea(
              webMercatorGeometry as __esri.Polygon,
              'square-meters'
            );
            console.log('geometryEngine.planarArea结果:', area);

            // 如果结果为0或NaN，尝试使用geodesicArea
            if (!area || area === 0 || isNaN(area)) {
              area = geometryEngine.geodesicArea(
                geometry as __esri.Polygon,
                'square-meters'
              );
              console.log('geometryEngine.geodesicArea结果:', area);
            }

            // 如果还是0，尝试使用项目中的calcArea函数
            if (!area || area === 0 || isNaN(area)) {
              area = calcArea(geometry as __esri.Polygon);
              console.log('calcArea结果:', area);
            }
          } catch (areaError) {
            console.error('面积计算错误:', areaError);
            // 最后尝试使用calcArea
            try {
              area = calcArea(geometry as __esri.Polygon);
              console.log('备用calcArea结果:', area);
            } catch (backupError) {
              console.error('备用面积计算也失败:', backupError);
            }
          }

          try {
            // 计算周长
            perimeter = geometryEngine.planarLength(
              webMercatorGeometry as __esri.Polygon,
              'meters'
            );

            // 如果周长为0，尝试geodesicLength
            if (!perimeter || perimeter === 0) {
              perimeter = geometryEngine.geodesicLength(
                geometry as __esri.Polygon,
                'meters'
              );
            }
          } catch (perimeterError) {
            console.error('周长计算错误:', perimeterError);
          }

          console.log('原始计算结果:', { area, perimeter });

          state.results.area = Math.abs(area); // 确保面积为正数
          state.results.perimeter = Math.abs(perimeter); // 确保周长为正数

          console.log('最终存储结果:', {
            area: state.results.area,
            perimeter: state.results.perimeter
          });
        } else {
          console.warn('面积测量但几何类型不是polygon:', geometry.type);
        }
        break;
    }

    console.log('当前state.results:', state.results);
    updateResults();
    SLMessage.success('测量完成');
  } catch (error) {
    console.error('测量计算错误:', error);
    SLMessage.error('测量计算失败');
    state.measuring = false;
  }
};

// 更新显示结果
const updateResults = () => {
  console.log('开始更新显示结果...');

  if (!refForm.value?.dataForm) {
    console.warn('表单数据不存在');
    return;
  }

  const formData = refForm.value.dataForm;
  console.log('当前表单数据:', formData);
  console.log('当前测量结果:', state.results);

  // 更新距离显示
  if (state.results.distance !== undefined) {
    const distanceUnit = formData.distanceUnit || 'meters';
    const convertedDistance = convertLength(
      state.results.distance,
      'meters',
      distanceUnit
    );
    formData.distance = `${convertedDistance.toFixed(2)}`;
    console.log('更新距离显示:', formData.distance);
  }

  // 更新周长显示
  if (state.results.perimeter !== undefined) {
    const perimeterUnit = formData.perimeterUnit || 'meters';
    const convertedPerimeter = convertLength(
      state.results.perimeter,
      'meters',
      perimeterUnit
    );
    formData.perimeter = `${convertedPerimeter.toFixed(2)}`;
    console.log('更新周长显示:', formData.perimeter);
  }

  // 更新面积显示
  if (state.results.area !== undefined) {
    const areaUnit = formData.areaUnit || 'square-meters';
    const convertedArea = convertArea(
      state.results.area,
      'square-meters',
      areaUnit
    );
    formData.area = `${convertedArea.toFixed(2)}`;
    console.log('更新面积显示:', formData.area);
  } else {
    console.log('面积结果为undefined，不更新显示');
  }

  console.log('更新后的表单数据:', formData);
};

// 长度单位转换
const convertLength = (
  value: number,
  fromUnit: string,
  toUnit: string
): number => {
  const meterValue =
    fromUnit === 'meters'
      ? value
      : fromUnit === 'kilometers'
        ? value * 1000
        : value;

  return toUnit === 'meters'
    ? meterValue
    : toUnit === 'kilometers'
      ? meterValue / 1000
      : meterValue;
};

// 面积单位转换
const convertArea = (
  value: number,
  fromUnit: string,
  toUnit: string
): number => {
  const squareMeterValue =
    fromUnit === 'square-meters'
      ? value
      : fromUnit === 'square-kilometers'
        ? value * 1000000
        : fromUnit === 'hectares'
          ? value * 10000
          : value;

  return toUnit === 'square-meters'
    ? squareMeterValue
    : toUnit === 'square-kilometers'
      ? squareMeterValue / 1000000
      : toUnit === 'hectares'
        ? squareMeterValue / 10000
        : squareMeterValue;
};

// 初始化
onMounted(async () => {
  if (!props.view) {
    console.error('地图视图未提供');
    return;
  }

  try {
    // 创建图形图层
    staticState.graphicsLayer = getGraphicLayer(props.view, {
      id: 'measure-graphics',
      title: '测量图形'
    });

    // 初始化绘制工具
    staticState.sketch = initSketch(props.view, staticState.graphicsLayer, {
      updateCallBack: resolveDrawEnd,
      createCallBack: resolveDrawEnd
    });

    console.log('测量工具初始化完成');

    // 等待下一个tick后自动开始默认测量类型
    await nextTick();
    startMeasure();
  } catch (error) {
    console.error('测量工具初始化失败:', error);
    SLMessage.error('测量工具初始化失败');
  }
});

// 清理
onBeforeUnmount(() => {
  try {
    destroySketch();
    if (staticState.graphicsLayer && props.view) {
      props.view.map.remove(staticState.graphicsLayer);
    }
    console.log('测量工具已清理');
  } catch (error) {
    console.error('测量工具清理失败:', error);
  }
});
</script>

<style lang="scss" scoped>
.measure-container {
  width: 280px;
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-form-item__content) {
  margin-left: 0 !important;
}

:deep(.el-input__inner) {
  background-color: #f5f7fa;
}

:deep(.el-button) {
  border-radius: 4px;
}

:deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-button--danger) {
  background-color: #f56c6c;
  border-color: #f56c6c;
}

/* 单位下拉框样式 */
:deep(.el-select) {
  margin-left: 8px;
  width: 100px !important;
  min-width: 100px !important;
}

:deep(.el-select .el-input) {
  width: 80px !important;
}

:deep(.el-select .el-input__inner) {
  padding: 0 20px 0 8px !important;
  text-align: center;
  width: 80px !important;
  min-width: 80px !important;
}

:deep(.el-select .el-input__suffix) {
  right: 4px;
}

/* 下拉面板宽度 */
:deep(.el-select-dropdown) {
  min-width: 60px !important;
}

/* 表单项间距 */
:deep(.el-form-item) {
  margin-bottom: 12px;
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
}
</style>
