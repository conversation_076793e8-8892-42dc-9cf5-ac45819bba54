import { request } from '@/plugins/axios'

/**
 * 创建工程
 * @param {Object} projectData 工程数据
 * @returns {Promise}
 */
export const createProject = (projectData) => {
  return request({
    url: '/api/pipeline-collection/projects',
    method: 'post',
    data: projectData
  })
}

/**
 * 生成工程编号
 * @returns {Promise}
 */
export const generateProjectCode = () => {
  return request({
    url: '/api/pipeline-collection/projects/generate-code',
    method: 'get'
  })
}

/**
 * 获取工程列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getProjectList = (params) => {
  return request({
    url: '/api/pipeline-collection/projects',
    method: 'get',
    params
  })
}

/**
 * 获取工程详情
 * @param {string} id 工程ID
 * @returns {Promise}
 */
export const getProjectDetail = (id) => {
  return request({
    url: `/api/pipeline-collection/projects/${id}`,
    method: 'get'
  })
}

/**
 * 更新工程
 * @param {string} id 工程ID
 * @param {Object} projectData 工程数据
 * @returns {Promise}
 */
export const updateProject = (id, projectData) => {
  return request({
    url: `/api/pipeline-collection/projects/${id}`,
    method: 'put',
    data: projectData
  })
}

/**
 * 删除工程
 * @param {string} id 工程ID
 * @returns {Promise}
 */
export const deleteProject = (id) => {
  return request({
    url: `/api/pipeline-collection/projects/${id}`,
    method: 'delete'
  })
}

/**
 * 验证工程编号唯一性
 * @param {string} code 工程编号
 * @returns {Promise}
 */
export const validateProjectCode = (code) => {
  return request({
    url: '/api/pipeline-collection/projects/validate-code',
    method: 'post',
    data: { code }
  })
}

/**
 * 提交工程审核
 * @param {string} id 工程ID
 * @returns {Promise}
 */
export const submitProject = (id) => {
  return request({
    url: `/api/pipeline-collection/projects/${id}/submit`,
    method: 'post'
  })
}