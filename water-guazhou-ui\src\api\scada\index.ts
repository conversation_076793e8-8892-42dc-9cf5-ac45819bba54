import request from '@/plugins/axios';

//  根据发起者ID获取组态列表  GET api/dashChart/findByOriginatorId?originatorId=
// projectId 发起者ID，即企业ID，项目ID，网关ID等，不带-
export function getFindScadaList(params) {
  return request({
    // url: 'api/dashChart/findByOriginatorId',
    url: '/api/zutai/dashboard-list',
    method: 'get',
    params
  });
}

// 删除组态  DELETE api/dashChart/delete/?id=&token=
// id :组态ID，从对象中的dashboardId获取
// token：登录组态的token
export function deleteScada(id) {
  return request({
    url: `/api/zutai/dashboard-list?id=${id}`,
    // url: 'api/dashChart/delete/?id=' + params.id + '&token=' + params.token,
    method: 'delete'
  });
}
