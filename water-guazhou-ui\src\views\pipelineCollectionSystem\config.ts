// 管网数据采集系统 - 工程管理配置

/**
 * 工程性质选项
 */
export const getProjectNatureOptions = () => [
  { label: '新建工程', value: 'new' },
  { label: '改建工程', value: 'rebuild' },
  { label: '扩建工程', value: 'expand' },
  { label: '维修工程', value: 'repair' },
  { label: '检测工程', value: 'inspection' },
  { label: '维护工程', value: 'maintenance' }
];

/**
 * 所属区划选项
 * 根据瓜州县实际行政区划设置
 */
export const getDistrictOptions = () => [
  { label: '瓜州县', value: 'guazhou' },
  { label: '锁阳城镇', value: 'suoyangcheng' },
  { label: '双塔乡', value: 'shuangta' },
  { label: '布隆吉乡', value: 'bulongji' },
  { label: '梁湖乡', value: 'lianghu' },
  { label: '河东乡', value: 'hedong' },
  { label: '腰站子乡', value: 'yaozhan<PERSON>' },
  { label: '三道沟镇', value: 'sandaogou' },
  { label: '西湖镇', value: 'xihu' },
  { label: '南岔镇', value: 'nancha' },
  { label: '广至乡', value: 'guangzhi' },
  { label: '柳园镇', value: 'liuyuan' }
];

/**
 * 测量单位选项
 */
export const getMeasurementUnitOptions = () => [
  { label: '米(m)', value: 'm' },
  { label: '千米(km)', value: 'km' },
  { label: '平方米(m²)', value: 'm2' },
  { label: '立方米(m³)', value: 'm3' },
  { label: '公里(km)', value: 'kilometer' },
  { label: '厘米(cm)', value: 'cm' },
  { label: '毫米(mm)', value: 'mm' }
];

/**
 * 工程状态选项
 */
export const getProjectStatusOptions = () => [
  { label: '计划中', value: 'planned' },
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'completed' },
  { label: '已暂停', value: 'paused' },
  { label: '已取消', value: 'cancelled' }
];

/**
 * 审核状态选项
 */
export const getAuditStatusOptions = () => [
  { label: '待审核', value: 'pending' },
  { label: '审核中', value: 'reviewing' },
  { label: '审核通过', value: 'approved' },
  { label: '审核驳回', value: 'rejected' },
  { label: '重新提交', value: 'resubmitted' }
];

/**
 * 工程优先级选项
 */
export const getProjectPriorityOptions = () => [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
];

/**
 * 工程类型选项
 */
export const getProjectTypeOptions = () => [
  { label: '供水工程', value: 'water_supply' },
  { label: '排水工程', value: 'drainage' },
  { label: '管网工程', value: 'pipeline' },
  { label: '设施维护', value: 'facility_maintenance' },
  { label: '设备安装', value: 'equipment_installation' },
  { label: '质量检测', value: 'quality_inspection' }
];

/**
 * 工程预算范围选项
 */
export const getBudgetRangeOptions = () => [
  { label: '1万以下', value: 'under_10k' },
  { label: '1-5万', value: '10k_50k' },
  { label: '5-10万', value: '50k_100k' },
  { label: '10-50万', value: '100k_500k' },
  { label: '50-100万', value: '500k_1m' },
  { label: '100万以上', value: 'over_1m' }
];

/**
 * 工程字段标签映射
 */
export const PROJECT_FIELD_LABELS = {
  fullName: '工程全称',
  projectCode: '工程编号',
  shortName: '工程简称',
  nature: '工程性质',
  district: '所属区划',
  measurementUnit: '测量单位',
  assignedStaff: '指派员工',
  workScope: '工程范围',
  workScopeDescription: '工程范围描述',
  startDate: '开始日期',
  endDate: '结束日期',
  status: '工程状态',
  auditStatus: '审核状态',
  priority: '优先级',
  type: '工程类型',
  budgetRange: '预算范围',
  remark: '备注'
};

/**
 * 表单验证规则
 */
export const PROJECT_VALIDATION_RULES = {
  fullName: [
    { required: true, message: '请输入工程全称' },
    { min: 2, max: 100, message: '工程全称长度在2到100个字符' }
  ],
  projectCode: [
    { required: true, message: '请输入工程编号' },
    { pattern: /^[A-Z0-9\-]+$/, message: '工程编号只能包含大写字母、数字和横线' }
  ],
  shortName: [
    { required: true, message: '请输入工程简称' },
    { min: 2, max: 50, message: '工程简称长度在2到50个字符' }
  ],
  nature: [
    { required: true, message: '请选择工程性质' }
  ],
  district: [
    { required: true, message: '请选择所属区划' }
  ],
  measurementUnit: [
    { required: true, message: '请选择测量单位' }
  ],
  assignedStaff: [
    { required: true, message: '请选择指派员工' }
  ],
  workScopeDescription: [
    { required: true, message: '请输入工程范围描述' },
    { min: 10, max: 500, message: '工程范围描述长度在10到500个字符' }
  ],
  startDate: [
    { required: true, message: '请选择开始日期' }
  ],
  endDate: [
    { required: true, message: '请选择结束日期' }
  ]
};

/**
 * 默认表单值
 */
export const getDefaultProjectFormValues = (userId?: string) => ({
  createUserId: userId,
  status: 'planned',
  priority: 'medium'
});

/**
 * 日期格式化函数
 */
export const formatProjectDate = (date: string | Date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleDateString('zh-CN');
};

/**
 * 工程编号生成规则
 * 格式：GZ + 年份 + 月份 + 流水号（4位）
 */
export const generateProjectCodePattern = () => {
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  return `GZ${year}${month}`;
}; 