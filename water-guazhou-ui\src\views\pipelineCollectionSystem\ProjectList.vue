<!-- 管网数据采集系统-工程列表 -->
<template>
  <div class="project-list-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>工程管理</h2>
      <el-button type="primary" @click="handleNew" :icon="Plus">
        新建工程
      </el-button>
    </div>

    <!-- 搜索条件 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="工程全称：">
          <el-input 
            v-model="searchForm.fullName" 
            placeholder="请输入工程全称" 
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="所属区划：">
          <el-select 
            v-model="searchForm.district" 
            placeholder="请选择区划" 
            clearable
            style="width: 160px"
          >
            <el-option 
              v-for="item in getDistrictOptions()" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="测量单位：">
          <el-select 
            v-model="searchForm.measurementUnit" 
            placeholder="请选择测量单位" 
            clearable
            style="width: 140px"
          >
            <el-option 
              v-for="item in getMeasurementUnitOptions()" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            查询
          </el-button>
          <el-button @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据列表 -->
    <div class="table-section">
      <el-table 
        :data="tableData" 
        v-loading="loading"
        style="width: 100%"
        stripe
        border
      >
        <el-table-column prop="fullName" label="工程全称" width="150" show-overflow-tooltip />
        <el-table-column prop="projectCode" label="工程编号" width="140" />
        <el-table-column prop="nature" label="工程性质" width="100">
          <template #default="{ row }">
            <span>{{ getNatureLabel(row.nature) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="district" label="所属区划" width="120">
          <template #default="{ row }">
            <span>{{ getDistrictLabel(row.district) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="measurementUnit" label="测量单位" width="100">
          <template #default="{ row }">
            <span>{{ getMeasurementUnitLabel(row.measurementUnit) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="auditStatus" label="审核状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getAuditStatusType(row.auditStatus)">
              {{ getAuditStatusLabel(row.auditStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="endDate" label="结束日期" width="120">
          <template #default="{ row }">
            <span>{{ formatDate(row.endDate) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleView(row)">
              查看
            </el-button>
            <el-button type="warning" link @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button 
              type="success" 
              link 
              @click="handleSubmit(row)"
              :disabled="row.auditStatus === 'approved'"
            >
              提交
            </el-button>
            <el-button type="danger" link @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新建工程弹窗 -->
    <el-dialog
      v-model="newProjectVisible"
      title="新建工程"
      width="80%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="new-project-dialog"
      append-to-body

    >
      <NewProject @success="handleNewSuccess" @cancel="handleNewCancel" />
    </el-dialog>

    <!-- 查看工程弹窗 -->
    <el-dialog
      v-model="viewProjectVisible"
      title="查看工程详情"
      width="80%"
      append-to-body
      :close-on-click-modal="false"
    >
      <div v-if="currentProject" class="project-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工程全称">
            {{ currentProject.fullName }}
          </el-descriptions-item>
          <el-descriptions-item label="工程编号">
            {{ currentProject.projectCode }}
          </el-descriptions-item>
          <el-descriptions-item label="工程简称">
            {{ currentProject.shortName }}
          </el-descriptions-item>
          <el-descriptions-item label="工程性质">
            {{ getNatureLabel(currentProject.nature) }}
          </el-descriptions-item>
          <el-descriptions-item label="所属区划">
            {{ getDistrictLabel(currentProject.district) }}
          </el-descriptions-item>
          <el-descriptions-item label="测量单位">
            {{ getMeasurementUnitLabel(currentProject.measurementUnit) }}
          </el-descriptions-item>
          <el-descriptions-item label="开始日期">
            {{ formatDate(currentProject.startDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="结束日期">
            {{ formatDate(currentProject.endDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="审核状态">
            <el-tag :type="getAuditStatusType(currentProject.auditStatus)">
              {{ getAuditStatusLabel(currentProject.auditStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="指派员工" span="2">
            {{ currentProject.assignedStaffNames || '暂无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 编辑工程弹窗 -->
    <el-dialog
      v-model="editProjectVisible"
      title="编辑工程"
      width="80%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <NewProject 
        v-if="editProjectVisible && currentProject"
        :project-data="currentProject"
        @success="handleEditSuccess" 
        @cancel="handleEditCancel" 
      />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue';
import { Plus } from '@element-plus/icons-vue';
import { SLConfirm, SLMessage } from '@/utils/Message';
import { 
  getProjectList, 
  deleteProject, 
  submitProject 
} from '@/api/pipelineCollectionSystem/project';
import { 
  getDistrictOptions, 
  getMeasurementUnitOptions, 
  getProjectNatureOptions,
  getAuditStatusOptions 
} from './config';
import NewProject from './NewProject.vue';

// 搜索表单
const searchForm = reactive({
  fullName: '',
  district: '',
  measurementUnit: ''
});

// 表格数据
const tableData = ref<any[]>([]);
const loading = ref(false);

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
});

// 弹窗状态
const newProjectVisible = ref(false);
const viewProjectVisible = ref(false);
const editProjectVisible = ref(false);
const currentProject = ref<any>(null);

// 获取选项标签的辅助函数
const getNatureLabel = (value: string) => {
  const options = getProjectNatureOptions();
  return options.find(option => option.value === value)?.label || value;
};

const getDistrictLabel = (value: string) => {
  const options = getDistrictOptions();
  return options.find(option => option.value === value)?.label || value;
};

const getMeasurementUnitLabel = (value: string) => {
  const options = getMeasurementUnitOptions();
  return options.find(option => option.value === value)?.label || value;
};

const getAuditStatusLabel = (value: string) => {
  const options = getAuditStatusOptions();
  return options.find(option => option.value === value)?.label || value;
};

// 获取审核状态标签类型
const getAuditStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'pending': 'warning',
    'reviewing': 'primary',
    'approved': 'success',
    'rejected': 'danger',
    'resubmitted': 'info'
  };
  return typeMap[status] || 'info';
};

// 格式化日期
const formatDate = (date: string | Date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleDateString('zh-CN');
};

// 获取项目列表
const fetchProjectList = async () => {
  try {
    loading.value = true;
    const params = {
      ...searchForm,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    };
    
    const res = await getProjectList(params);
    
    if (res.data?.code === 200) {
      tableData.value = res.data.data?.list || [];
      pagination.total = res.data.data?.total || 0;
    } else {
      SLMessage.error(res.data?.message || '获取工程列表失败');
    }
  } catch (error: any) {
    console.error('获取工程列表失败:', error);
    SLMessage.error(error.message || '获取工程列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.currentPage = 1;
  fetchProjectList();
};

// 重置处理
const handleReset = () => {
  searchForm.fullName = '';
  searchForm.district = '';
  searchForm.measurementUnit = '';
  pagination.currentPage = 1;
  fetchProjectList();
};

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.currentPage = 1;
  fetchProjectList();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  fetchProjectList();
};

// 新建工程
const handleNew = () => {
  newProjectVisible.value = true;
};

const handleNewSuccess = () => {
  newProjectVisible.value = false;
  SLMessage.success('工程创建成功');
  fetchProjectList();
};

const handleNewCancel = () => {
  newProjectVisible.value = false;
};

// 查看工程
const handleView = (row: any) => {
  currentProject.value = { ...row };
  viewProjectVisible.value = true;
};

// 编辑工程
const handleEdit = (row: any) => {
  currentProject.value = { ...row };
  editProjectVisible.value = true;
};

const handleEditSuccess = () => {
  editProjectVisible.value = false;
  SLMessage.success('工程更新成功');
  fetchProjectList();
};

const handleEditCancel = () => {
  editProjectVisible.value = false;
  currentProject.value = null;
};

// 提交工程
const handleSubmit = async (row: any) => {
  try {
    await SLConfirm('确定要提交该工程进行审核吗？');
    
    const res = await submitProject(row.id);
    
    if (res.data?.code === 200) {
      SLMessage.success('工程提交成功');
      fetchProjectList();
    } else {
      SLMessage.error(res.data?.message || '工程提交失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('提交工程失败:', error);
      SLMessage.error(error.message || '工程提交失败');
    }
  }
};

// 删除工程
const handleDelete = async (row: any) => {
  try {
    await SLConfirm('确定要删除该工程吗？删除后无法恢复！', '警告');
    
    const res = await deleteProject(row.id);
    
    if (res.data?.code === 200) {
      SLMessage.success('工程删除成功');
      fetchProjectList();
    } else {
      SLMessage.error(res.data?.message || '工程删除失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除工程失败:', error);
      SLMessage.error(error.message || '工程删除失败');
    }
  }
};

// 页面初始化
onMounted(() => {
  fetchProjectList();
});
</script>

<style lang="scss" scoped>
.project-list-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .el-form {
    .el-form-item {
      margin-bottom: 0;
    }
  }
}

.table-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .el-table {
    margin-bottom: 20px;
  }
}

.pagination-section {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

:deep(.new-project-dialog) {
  .el-dialog__body {
    padding: 0;
  }
}

.project-detail {
  padding: 20px;
}

@media (max-width: 768px) {
  .project-list-page {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .search-section {
    .el-form {
      .el-form-item {
        width: 100%;
        margin-bottom: 16px;

        .el-input,
        .el-select {
          width: 100% !important;
        }
      }
    }
  }

  .table-section {
    overflow-x: auto;
  }
}
</style> 