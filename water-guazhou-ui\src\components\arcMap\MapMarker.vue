<template>
  <div class="marker-container">
    <Form ref="refForm" :config="FormConfig"></Form>
  </div>
</template>

<script lang="ts" setup>
import { getGraphicLayer } from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';
import { useSketch } from '@/hooks/arcgis';
import Graphic from '@arcgis/core/Graphic.js';
import SimpleMarkerSymbol from '@arcgis/core/symbols/SimpleMarkerSymbol.js';
import SimpleLineSymbol from '@arcgis/core/symbols/SimpleLineSymbol.js';
import SimpleFillSymbol from '@arcgis/core/symbols/SimpleFillSymbol.js';
import TextSymbol from '@arcgis/core/symbols/TextSymbol.js';

const props = defineProps<{
  view?: __esri.MapView;
  telport?: string;
}>();

const state = reactive<{
  creating: boolean;
  markerType: 'point' | 'line' | 'polygon';
  markers: __esri.Graphic[];
  labelGraphics: Map<string, __esri.Graphic>;
}>({
  creating: false,
  markerType: 'point',
  markers: [],
  labelGraphics: new Map()
});

const staticState: {
  sketch?: __esri.SketchViewModel;
  graphicsLayer?: __esri.GraphicsLayer;
} = {};

const refForm = ref<IFormIns>();

// 获取当前标记类型的大小范围
const getSizeRange = () => {
  switch (state.markerType) {
    case 'point':
      return { min: 6, max: 30, default: 12 };
    case 'line':
      return { min: 1, max: 10, default: 3 };
    case 'polygon':
      return { min: 1, max: 8, default: 2 };
    default:
      return { min: 1, max: 30, default: 12 };
  }
};

const FormConfig = reactive<IFormConfig>({
  labelWidth: 50,
  group: [
    {
      fieldset: {
        desc: '标记工具'
      },
      fields: [
        {
          type: 'btn-group',
          label: '',
          btns: [
            {
              perm: true,
              title: '点标记',
              type: () =>
                state.markerType === 'point' ? 'primary' : 'default',
              iconifyIcon: 'mdi:map-marker',
              click: () => setMarkerType('point'),
              styles: { flex: '1', minWidth: '0' }
            },
            {
              perm: true,
              title: '线标记',
              type: () => (state.markerType === 'line' ? 'primary' : 'default'),
              iconifyIcon: 'mdi:vector-line',
              click: () => setMarkerType('line'),
              styles: { flex: '1', minWidth: '0' }
            },
            {
              perm: true,
              title: '面标记',
              type: () =>
                state.markerType === 'polygon' ? 'primary' : 'default',
              iconifyIcon: 'mdi:vector-polygon',
              click: () => setMarkerType('polygon'),
              styles: { flex: '1', minWidth: '0' }
            },
            {
              perm: true,
              title: '清除全部',
              type: 'danger',
              iconifyIcon: 'mdi:delete-sweep',
              click: () => clearAllMarkers(),
              styles: { flex: '1', minWidth: '0' }
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '标记样式'
      },
      fields: [
        {
          type: 'input',
          field: 'markerLabel',
          label: '标签',
          placeholder: '输入标记标签',
          labelWidth: '40px'
        },
        {
          type: 'color-picker',
          field: 'markerColor',
          label: '颜色',
          labelWidth: '40px',
          colorType: 'rgba'
        },
        {
          type: 'number',
          field: 'markerSize',
          label: '大小',
          labelWidth: '40px',
          min: 1,
          max: 30,
          suffix: 'px'
        }
      ]
    }
  ],
  gutter: 16,
  defaultValue: {
    markerLabel: '',
    markerColor: 'rgba(255, 0, 0, 1)',
    markerSize: 12
  }
});

const { initSketch, destroySketch } = useSketch();

// 设置标记类型并自动开始创建
const setMarkerType = (type: 'point' | 'line' | 'polygon') => {
  if (state.creating) {
    staticState.sketch?.cancel();
    state.creating = false;
  }

  state.markerType = type;

  // 更新大小默认值和范围
  const sizeRange = getSizeRange();
  if (refForm.value?.dataForm) {
    refForm.value.dataForm.markerSize = sizeRange.default;
  }

  console.log('切换标记类型:', type);
  startCreateMarker();
};

// 开始创建标记
const startCreateMarker = () => {
  if (!staticState.sketch) {
    SLMessage.error('标记工具未初始化');
    return;
  }

  state.creating = true;

  switch (state.markerType) {
    case 'point':
      staticState.sketch.create('point');
      break;
    case 'line':
      staticState.sketch.create('polyline');
      break;
    case 'polygon':
      staticState.sketch.create('polygon');
      break;
  }

  console.log('开始创建标记:', state.markerType);
};

// 清除所有标记
const clearAllMarkers = () => {
  if (state.markers.length === 0) {
    SLMessage.warning('没有标记可清除');
    return;
  }

  staticState.graphicsLayer?.removeAll();
  state.markers = [];
  state.labelGraphics.clear();

  // 清除后自动开始新的标记创建
  setTimeout(() => {
    startCreateMarker();
  }, 100);
};

// 处理绘制完成
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  if (result.state === 'complete' && result.graphics[0]) {
    const geometry = result.graphics[0].geometry;
    createMarkerGraphic(geometry);
    state.creating = false;

    // 自动开始创建下一个标记
    setTimeout(() => {
      startCreateMarker();
    }, 100);
  }
};

// 创建标记图形
const createMarkerGraphic = (geometry: __esri.Geometry) => {
  try {
    const formData = refForm.value?.dataForm;
    const label = formData?.markerLabel || '';
    const color = formData?.markerColor || 'rgba(255, 0, 0, 1)';
    const size = formData?.markerSize || getSizeRange().default;

    console.log('创建标记 - 输入参数:', {
      label,
      color,
      size,
      geometryType: geometry.type
    });

    let symbol: __esri.Symbol;

    switch (geometry.type) {
      case 'point':
        symbol = createPointSymbol(color, size);
        break;
      case 'polyline':
        symbol = createLineSymbol(color, size);
        break;
      case 'polygon':
        symbol = createPolygonSymbol(color, size);
        break;
      default:
        console.warn('不支持的几何类型:', geometry.type);
        return;
    }

    const markerId = Date.now().toString();
    const graphic = new Graphic({
      geometry: geometry,
      symbol: symbol,
      attributes: {
        id: markerId,
        type: state.markerType,
        label: label,
        color: color,
        size: size,
        createTime: new Date().toLocaleString()
      }
    });

    staticState.graphicsLayer?.add(graphic);
    state.markers.push(graphic);

    // 如果有标签，添加文本标记
    if (label.trim()) {
      const textGraphic = createTextGraphic(geometry, label, markerId);
      staticState.graphicsLayer?.add(textGraphic);
      state.labelGraphics.set(markerId, textGraphic);
      console.log('标签创建完成:', { markerId, label });
    }
  } catch (error) {
    console.error('标记创建错误:', error);
    SLMessage.error('标记创建失败');
    state.creating = false;
  }
};

// 创建点符号
const createPointSymbol = (
  color: string,
  size: number
): __esri.SimpleMarkerSymbol => {
  const [r, g, b, a] = parseRGBAColor(color);
  const finalColor = [r, g, b, Math.round(a * 255)];
  console.log('创建点符号 - 最终颜色:', finalColor);

  return new SimpleMarkerSymbol({
    style: 'circle',
    color: finalColor,
    size: size,
    outline: { color: [255, 255, 255], width: 2 }
  });
};

// 创建线符号
const createLineSymbol = (
  color: string,
  size: number
): __esri.SimpleLineSymbol => {
  const [r, g, b, a] = parseRGBAColor(color);
  const finalColor = [r, g, b, Math.round(a * 255)];
  console.log('创建线符号 - 最终颜色:', finalColor);

  return new SimpleLineSymbol({
    style: 'solid',
    color: finalColor,
    width: size
  });
};

// 创建面符号
const createPolygonSymbol = (
  color: string,
  size: number
): __esri.SimpleFillSymbol => {
  const [r, g, b, a] = parseRGBAColor(color);
  const finalColor = [r, g, b, Math.round(a * 255)];
  console.log('创建面符号 - 最终颜色:', finalColor);

  return new SimpleFillSymbol({
    style: 'solid',
    color: finalColor, // 使用用户设置的透明度
    outline: { color: finalColor, width: size }
  });
};

// 创建文本图形
const createTextGraphic = (
  geometry: __esri.Geometry,
  text: string,
  markerId: string
): __esri.Graphic => {
  let textPoint: __esri.Point;

  // 根据几何类型确定文本位置
  switch (geometry.type) {
    case 'point':
      textPoint = geometry as __esri.Point;
      break;
    case 'polyline':
      // 线的中点
      const polyline = geometry as __esri.Polyline;
      textPoint = polyline.extent.center;
      break;
    case 'polygon':
      // 面的中心点
      const polygon = geometry as __esri.Polygon;
      textPoint = polygon.centroid;
      break;
    default:
      console.warn('无法为几何类型创建标签:', geometry.type);
      return new Graphic();
  }

  const textSymbol = new TextSymbol({
    text: text,
    color: [0, 0, 0], // 黑色文字
    haloColor: [255, 255, 255], // 白色光晕
    haloSize: 1,
    font: {
      size: 12,
      family: 'Arial',
      weight: 'bold'
    }
  });

  console.log('创建文本图形:', { text, position: textPoint, markerId });

  return new Graphic({
    geometry: textPoint,
    symbol: textSymbol,
    attributes: {
      isLabel: true,
      parentId: markerId,
      labelText: text
    }
  });
};

// 解析RGBA颜色
const parseRGBAColor = (rgba: string): [number, number, number, number] => {
  console.log('解析颜色:', rgba);

  // 处理rgba(r, g, b, a)格式
  const rgbaMatch = rgba.match(
    /rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)/
  );
  if (rgbaMatch) {
    const result: [number, number, number, number] = [
      parseInt(rgbaMatch[1]),
      parseInt(rgbaMatch[2]),
      parseInt(rgbaMatch[3]),
      rgbaMatch[4] ? parseFloat(rgbaMatch[4]) : 1
    ];
    console.log('解析结果 (RGBA):', result, '转换后:', [
      result[0],
      result[1],
      result[2],
      Math.round(result[3] * 255)
    ]);
    return result;
  }

  // 处理十六进制格式 #RRGGBB
  if (rgba.startsWith('#')) {
    const hex = rgba.slice(1);
    if (hex.length === 6) {
      const result: [number, number, number, number] = [
        parseInt(hex.slice(0, 2), 16),
        parseInt(hex.slice(2, 4), 16),
        parseInt(hex.slice(4, 6), 16),
        1
      ];
      console.log('解析结果 (HEX):', result);
      return result;
    }
    // 处理十六进制格式 #RRGGBBAA
    if (hex.length === 8) {
      const result: [number, number, number, number] = [
        parseInt(hex.slice(0, 2), 16),
        parseInt(hex.slice(2, 4), 16),
        parseInt(hex.slice(4, 6), 16),
        parseInt(hex.slice(6, 8), 16) / 255
      ];
      console.log('解析结果 (HEX with Alpha):', result);
      return result;
    }
  }

  // 默认红色
  console.log('使用默认颜色 (红色)');
  return [255, 0, 0, 1];
};

// 获取标记类型名称
const getMarkerTypeName = (type: string): string => {
  const nameMap: Record<string, string> = {
    point: '点',
    line: '线',
    polygon: '面'
  };
  return nameMap[type] || type;
};

// 初始化
onMounted(async () => {
  if (!props.view) {
    console.error('地图视图未提供');
    return;
  }

  try {
    staticState.graphicsLayer = getGraphicLayer(props.view, {
      id: 'marker-graphics',
      title: '地图标记'
    });

    staticState.sketch = initSketch(props.view, staticState.graphicsLayer, {
      updateCallBack: resolveDrawEnd,
      createCallBack: resolveDrawEnd
    });

    console.log('标记工具初始化完成');

    await nextTick();
    // 设置默认大小
    if (refForm.value?.dataForm) {
      refForm.value.dataForm.markerSize = getSizeRange().default;
    }
    startCreateMarker();
  } catch (error) {
    console.error('标记工具初始化失败:', error);
    SLMessage.error('标记工具初始化失败');
  }
});

// 清理
onBeforeUnmount(() => {
  try {
    destroySketch();
    if (staticState.graphicsLayer && props.view) {
      props.view.map.remove(staticState.graphicsLayer);
    }
    console.log('标记工具已清理');
  } catch (error) {
    console.error('标记工具清理失败:', error);
  }
});
</script>

<style lang="scss" scoped>
.marker-container {
  width: 280px;
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-form-item__content) {
  margin-left: 0 !important;
}

:deep(.el-input__inner) {
  background-color: #f5f7fa;
}

:deep(.el-button) {
  border-radius: 4px;
}

:deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-button--danger) {
  background-color: #f56c6c;
  border-color: #f56c6c;
}

// :deep(.el-form-item) {
//   margin-bottom: 8px;
// }

// :deep(.el-form-item:last-child) {
//   margin-bottom: 0;
// }

/* 颜色选择器样式 */
:deep(.el-color-picker) {
  width: 100%;
}

/* 数字输入框样式 */
:deep(.el-input-number) {
  width: 100%;
}
</style>
