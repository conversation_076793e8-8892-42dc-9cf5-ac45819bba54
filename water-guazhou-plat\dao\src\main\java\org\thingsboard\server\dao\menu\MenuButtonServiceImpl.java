package org.thingsboard.server.dao.menu;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.menu.Menu;
import org.thingsboard.server.common.data.menu.MenuPool;
import org.thingsboard.server.common.data.menu.MenuPoolVO;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.dao.model.sql.MenuButtonEntity;
import org.thingsboard.server.dao.model.sql.MenuButtonRole;
import org.thingsboard.server.dao.role.RoleService;
import org.thingsboard.server.dao.sql.menu.MenuButtonRepository;
import org.thingsboard.server.dao.sql.menu.MenuButtonRoleRepository;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MenuButtonServiceImpl implements MenuButtonService {

    @Autowired
    private MenuButtonRepository menuButtonRepository;

    @Autowired
    private MenuButtonRoleRepository menuButtonRoleRepository;

    @Autowired
    private RoleService roleService;

    @Autowired
    private MenuTenantService menuTenantService;

    @Autowired
    private MenuPoolService menuPoolService;


    @Override
    public List<MenuButtonEntity> findList(String menuId) {
        return menuButtonRepository.findByMenuIdOrderByOrderNum(menuId);
    }

    @Override
    public MenuButtonEntity save(MenuButtonEntity buttonEntity) {
        if (StringUtils.isBlank(buttonEntity.getId())) {
            buttonEntity.setCreateTime(new Date());
        }

        return menuButtonRepository.save(buttonEntity);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        for (String id : ids) {
            menuButtonRepository.delete(id);
        }
    }

    @Override
    @Transactional
    public void setMenuButtonList(String roleId, List<String> menuButtonIdList) {
        // 按角色ID删除按钮权限
        menuButtonRoleRepository.deleteByRoleId(roleId);

        Set<String> buttonList = new HashSet<>();

        List<MenuPool> menuList = menuPoolService.findAll();
        Map<String, MenuPool> menuPoolMap = new HashMap<>();
        if (menuList != null && menuList.size() > 0) {
            menuList.forEach(menuPool -> menuPoolMap.put(menuPool.getUuidId().toString(), menuPool));
        }

        for (String id : menuButtonIdList) {
            if (menuPoolMap.containsKey(id)) {// 为菜单，查询菜单下的按钮
                MenuPool menuPool = menuPoolMap.get(id);
                if (menuPool.getParentId().equals(new MenuPoolId(UUIDConverter.fromString("1b21dd2192ef4708080808080808080")))) {// 顶级菜单
                    // 查询顶级菜单下的所有按钮
                    List<MenuPool> childList = menuPoolService.findByParentId(menuPool.getId());
                    if (childList != null && childList.size() > 0) {
                        List<String> menuIdList = childList.stream()
                                .map(menu -> UUIDConverter.fromTimeUUID(menu.getUuidId()))
                                .collect(Collectors.toList());

                        // 获取菜单下的按钮列表
                        if (menuIdList.size() > 0) {
                            List<MenuButtonEntity> list = menuButtonRepository.findByMenuIdIn(menuIdList);
                            if (list != null && list.size() > 0) {
                                List<String> idList = list.stream().map(MenuButtonEntity::getId).collect(Collectors.toList());
                                if (idList.size() > 0) {
                                    buttonList.addAll(idList);
                                }
                            }
                        }
                    }
                } else {
                    // 获取此菜单下的所有按钮权限
                    List<MenuButtonEntity> list = menuButtonRepository.findByMenuIdIn(Collections.singletonList(UUIDConverter.fromTimeUUID(menuPool.getUuidId())));
                    if (list != null && list.size() > 0) {
                        List<String> idList = list.stream().map(MenuButtonEntity::getId).collect(Collectors.toList());
                        if (idList.size() > 0) {
                            buttonList.addAll(idList);
                        }
                    }
                }
            } else {
                buttonList.add(id);
            }
        }

        List<String> addButtonList = new ArrayList<>(buttonList);
        // 保存角色新的按钮权限
        if (addButtonList.size() > 0) {
            List<MenuButtonRole> list = new ArrayList<>();
            for (String menuButtonId : menuButtonIdList) {
                MenuButtonRole menuButtonRole = new MenuButtonRole();
                menuButtonRole.setRoleId(roleId);
                if (roleId.equals("TENANT_PROMOTE") || roleId.equals("TENANT_ADMIN") || roleId.equals("TENANT_SUPPORT")) {
                    // TODO 预留小角色添加
                } else {
                    menuButtonRole.setMenuButtonId(menuButtonId);
                }

                list.add(menuButtonRole);
            }

            menuButtonRoleRepository.save(list);
        }

    }

    @Override
    public List<MenuButtonEntity> getCurrentUserMenuButtonList(UserId id, Authority authority) {
        List<MenuButtonRole> menuButtonRoleList = new ArrayList<>();
        if (Authority.TENANT_PROMOTE.equals(authority)) {
            // 查询角色的按钮权限列表
            menuButtonRoleList = menuButtonRoleRepository.findByRoleId("TENANT_PROMOTE");
        } else if (Authority.TENANT_SUPPORT.equals(authority)) {
            // 查询角色的按钮权限列表
            menuButtonRoleList = menuButtonRoleRepository.findByRoleId("TENANT_SUPPORT");
        } else if (Authority.TENANT_ADMIN.equals(authority)) {
            // 查询角色的按钮权限列表
            menuButtonRoleList = menuButtonRoleRepository.findAll();
        } else if (Authority.CUSTOMER_USER.equals(authority)) {
            // 查询当前用户的角色ID
            String roleId = roleService.getRoleIdByUserId(id);
            // 查询角色的按钮权限列表
            menuButtonRoleList = menuButtonRoleRepository.findByRoleId(roleId);
        }

        // 查询按钮权限列表
        return menuButtonRepository.findByIdIn(menuButtonRoleList.stream()
                .map(MenuButtonRole::getMenuButtonId)
                .collect(Collectors.toList())
        );
    }

    @Override
    public Object tree(User currentUser) {
        List<MenuPoolVO> menuList = menuTenantService.findMenuPoolVOByTenantId(currentUser.getTenantId());
        if (menuList == null || menuList.isEmpty()) {
            return new ArrayList<>();
        }

        //TODO 2025/8/6 优化，不要三次for循环，提升效率。
        for (MenuPoolVO menu : menuList) {
            List<MenuPoolVO> children = menu.getChildren();
            if (children != null && !children.isEmpty()) {
                // 查询菜单下的按钮权限
                children.parallelStream().forEach(child -> {
                    List<MenuButtonEntity> buttonList = this.findList(UUIDConverter.fromTimeUUID(UUID.fromString(child.getId())));
                    if (buttonList != null && !buttonList.isEmpty()) {
                        List<MenuPoolVO> buttonChildrenList = buttonList.stream()
                                .map(buttonEntity -> {
                                    MenuPoolVO button = new MenuPoolVO();
                                    button.setId(buttonEntity.getId());
                                    button.setLabel(buttonEntity.getName());
                                    button.setNodeType("button");
                                    return button;
                                })
                                .collect(Collectors.toList());
                        child.setChildren(buttonChildrenList);
                    }
                });
            }
        }
        return menuList;
    }

    @Override
    public List<MenuButtonEntity> getRoleButtonList(String roleId) {
        // 查询角色的按钮权限列表
        List<MenuButtonRole> menuButtonRoleList = menuButtonRoleRepository.findByRoleId(roleId);

        return menuButtonRepository.findByIdIn(menuButtonRoleList.stream()
                .map(MenuButtonRole::getMenuButtonId)
                .collect(Collectors.toList())
        );
    }
}
