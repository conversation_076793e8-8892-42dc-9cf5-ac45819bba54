<!-- 沿线查询 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'沿线查询'"
    @map-loaded="onMapLoaded"
    @detail-refreshed="state.curOperate = ''"
  >
    <template #right-title>
      <SchemeHeader
        :title="'沿线查询'"
        @scheme-click="scheme.openManagerDialog"
      ></SchemeHeader>
    </template>
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <SchemeManage
      :ref="scheme.getSchemeManageRef"
      :type="scheme.schemeType.value"
      @row-click="handleUseScheme"
    ></SchemeManage>
    <SaveScheme
      :ref="scheme.getSaveSchemeRef"
      @submit="handleSchemeSubmit"
    ></SaveScheme>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import Graphic from '@arcgis/core/Graphic'
import {
  excuteIdentify,
  getGraphicLayer,
  getSubLayerIds,
  initIdentifyParams,
  setMapCursor,
  setSymbol
} from '@/utils/MapHelper'
import {
  excuteIdentifyByGeoserver,
  convertGeoJSONToArcGIS,
  getPipeAttachmentByGeoserver
} from '@/utils/geoserver/geoserverUtils.js'
// 导入沿线分析接口
import { alongAnalysis } from '@/utils/geoserver/gisAnalyse'

// @ts-ignore
import { IFormIns } from '@/components/type'
import { queryLayerClassName } from '@/api/mapservice'
import { SLMessage } from '@/utils/Message'
// @ts-ignore
import RightDrawerMap from '../common/RightDrawerMap.vue'
import { GetPipeAttachment } from '@/api/mapservice/pipe'
// @ts-ignore
import SchemeHeader from './Scheme/SchemeHeader.vue'
// @ts-ignore
import SchemeManage from './Scheme/SchemeManage.vue'
// @ts-ignore
import SaveScheme from './Scheme/SaveScheme.vue'
import { useScheme } from '@/hooks/arcgis/useScheme'

const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refForm = ref<IFormIns>()

const state = reactive<{
  tabs: any[]
  curOperate: 'picking' | 'searching' | ''
  layerInfos: any[]
  layerIds: any[]
}>({
  tabs: [],
  curOperate: '',
  layerInfos: [],
  layerIds: []
})

// 声明GIS_SERVER_SWITCH全局变量
declare global {
  interface Window {
    GIS_SERVER_SWITCH: boolean;
    // @ts-ignore
    SITE_CONFIG: any;
  }
}

// 定义GeoServer图层类型
interface GeoServerLayer {
  name: string;
  title?: string;
}

// 定义WMS图层类型
interface WMSLayer extends __esri.Layer {
  sublayers?: GeoServerLayer[];
}

const staticState: {
  view?: __esri.MapView
  graphicsLayer?: __esri.GraphicsLayer
  queryParams: {
    geometry?: __esri.Geometry
    where?: string
  }
  mapClick?: {
    remove(): void
  }
  identifyResults: __esri.Graphic[]
} = {
  queryParams: {
    geometry: undefined,
    where: undefined
  },
  identifyResults: []
}

const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '依次点选两个节点后进行查询'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: false,
          label: '选择管线图层',
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value'
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: '8px'
          },
          btns: [
            {
              perm: true,
              type: 'warning',
              text: () => (state.curOperate === 'picking' ? '拾取节点中...' : '拾取节点'),
              styles: {
                width: '100%'
              },
              loading: () => state.curOperate === 'picking',
              click: () => startPick()
            }
          ]
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: '8px'
          },
          btns: [
            {
              perm: true,
              text: () => (state.curOperate === 'searching' ? '查询中，请稍候...' : '查询'),
              styles: {
                width: '50%'
              },
              loading: () => state.curOperate === 'searching',
              disabled: () => state.curOperate === 'picking',
              click: () => doQuery()
            },
            {
              perm: true,
              text: '清除',
              styles: {
                width: '50%'
              },
              type: 'danger',
              disabled: () => state.curOperate === 'searching',
              click: () => stopIdentify()
            }
          ]
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: '8px'
          },
          btns: [
            {
              perm: window.SITE_CONFIG.GIS_CONFIG.gisSaveScheme,
              text: '保存方案',
              styles: {
                width: '100%'
              },
              click: () => handleSaveScheme()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})

const startPick = () => {
  if (!staticState.view) return
  stopIdentify()
  state.curOperate = 'picking'
  setMapCursor('crosshair')

  staticState.graphicsLayer?.removeAll()
  staticState.identifyResults = []
  staticState.mapClick = staticState.view?.on('click', async e => {
    await doIdentify(e)
  })
}

/**
 * 节点识别函数 - 用于沿线查询中识别用户点击的节点
 * 在沿线查询中，用户需要依次点选两个节点，然后查询这两个节点之间的管线
 */
const doIdentify = async (e: any) => {
  if (!staticState.view) return
  try {
    let feature: __esri.Graphic;

    const res = await excuteIdentifyByGeoserver(
      staticState.view,
      '/geoserver/guazhou/wms',
      '测点', // 使用选中的图层ID
      e
    )

    // 检查是否查询到节点
    if (!res || !res.data || !res.data.features || !res.data.features.length) {
      SLMessage.warning('没有查询到节点')
      return
    }

    // 将GeoJSON格式的几何对象转换为ArcGIS可以使用的几何对象
    const geoJsonFeature = res.data.features[0]
    const geometry = convertGeoJSONToArcGIS(geoJsonFeature.geometry)
    feature = new Graphic({
      geometry: geometry,
      attributes: geoJsonFeature.properties || {},
      symbol: setSymbol(geometry.type, { color: [255, 0, 0, 1] })
    })


    staticState.identifyResults.push(feature)
    staticState.graphicsLayer?.add(feature)

    if (staticState.identifyResults.length >= 2) {
      staticState.mapClick?.remove()
      state.curOperate = ''
      setMapCursor('')
      SLMessage.success('已选择两个节点，可以进行查询')
    }
  } catch (error) {
    console.error('节点查询失败:', error)
    staticState.view?.graphics.removeAll()
  }
}

/**
 * 执行沿线查询 - 查询两个节点之间的管线和设施
 * 在用户选择两个节点后，执行此函数查询节点之间的管线和设施信息
 */
const doQuery = async () => {
  // 检查是否已选择两个节点
  if (staticState.identifyResults.length !== 2) {
    SLMessage.warning('请依次选取两个节点后再试')
    return
  }
  // 获取两个节点的ID
  const startId = staticState.identifyResults[0].attributes['测点编号'] || staticState.identifyResults[0].attributes.OBJECTID
  const endId = staticState.identifyResults[1].attributes['测点编号'] || staticState.identifyResults[1].attributes.OBJECTID
  if (!startId || !endId) {
    SLMessage.error('节点ID获取失败')
    return
  }

  state.curOperate = 'searching'
  setMapCursor('progress')
  try {
    // 调用沿线分析接口
    const pageNum = 1;
    const pageSize = 20;
    const layer = refForm.value?.dataForm.layerid[0] || '管线';
    const res = await alongAnalysis({ startId, endId, layer})
    // 假设返回res.data为数组或对象，适配展示
    if (!res.data.features) {
      SLMessage.warning('未查询到沿线结果')
    } else {
      // 这里假设res为数组，或res.data为数组
      const dataArr = res.data.features
      const tabs = [{
        label: `沿线结果(${dataArr.length})`,
        name: '沿线结果',
        data: dataArr
      }]
      refMap.value?.refreshDetail(tabs)
    }
  } catch (error) {
    SLMessage.error('查询失败')
    console.error('沿线查询失败:', error)
  }
  setMapCursor('')
  state.curOperate = ''
}

/**
 * 获取图层信息 - 用于填充树状组件的选项
 * 在沿线查询中，用户需要选择要查询的图层，此函数获取可用的图层信息
 */
const getLayerInfo = () => {
  // GeoServer模式 - 从WMS图层获取子图层信息
  const field = FormConfig.group[0].fields[0] as IFormTree

  // 使用类型断言解决类型错误
  const layerInfo = (staticState.view?.layerViews as any)?.items?.[0]?.layer?.sublayers;
  if (layerInfo && layerInfo.items) {
    // 只筛选管线类（esriGeometryPolyline）
    let layers = layerInfo.items
      .filter((item: any) => (item.type && item.type.toLowerCase().includes('line')))
      .map((item: any) => {
        return {
          label: item.name, // 图层名称
          value: item.name, // 图层ID
        }
      });
    // 设置树状组件的选项
    field.options = layers;
    // 设置默认选中所有图层
    refForm.value && (refForm.value.dataForm.layerid = layers.map(item => item.value))
  }
}

const stopIdentify = () => {
  staticState.mapClick?.remove()
  staticState.identifyResults = []
  staticState.graphicsLayer?.removeAll()
  refMap.value?.clearDetailData()
  state.curOperate = ''
  setMapCursor('')
}

const scheme = useScheme('line')
const handleSaveScheme = () => {
  if (staticState.identifyResults.length < 2) {
    SLMessage.warning('请先在地图上选择两个节点')
    return
  }
  scheme.openSaveDialog()
}
const handleUseScheme = async (row: any) => {
  stopIdentify()
  const detail = scheme.parseScheme(row)
  if (refForm.value?.dataForm) {
    refForm.value.dataForm.layerid = detail.layerid || []
  }
  if (detail.identifyResults.length) {
    staticState.identifyResults = detail.identifyResults.map((item: any) => {
      return Graphic.fromJSON(item)
    })
    staticState.graphicsLayer?.removeAll()
    staticState.graphicsLayer?.addMany(staticState.identifyResults)
  }
  doQuery()
}
const handleSchemeSubmit = (params: any) => {
  scheme.submitScheme({
    ...params,
    type: scheme.schemeType.value,
    detail: JSON.stringify({
      layerid: refForm.value?.dataForm.layerid || [],
      identifyResults: staticState.identifyResults
    })
  })
}
const onMapLoaded = (view: __esri.MapView) => {
  staticState.view = view
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'search-line',
    title: '沿线查询'
  })
  setTimeout(()=>{
    getLayerInfo()
  },1000)
}
onBeforeUnmount(() => {
  staticState.mapClick?.remove()
  staticState.graphicsLayer?.removeAll()
})
</script>
<style lang="scss" scoped></style>
