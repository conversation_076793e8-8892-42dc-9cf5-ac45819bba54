<template>
  <div>
    <Panel
      ref="refPanel"
      custom-class="gis-detail-panel"
      :telport="telport"
      :draggable="false"
      :max-min="maxmin"
      :before-close="beforeClose"
      :extra="true"
    >
      <template #extra>
        <FormTableColumnFilter :columns="TableConfig_Detail.columns" :show-tooltip="true"></FormTableColumnFilter>
      </template>
      <template #header>
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <Search ref="refTab" class="pipe-detail" :config="TabFormConfig" />
          <el-button type="primary" size="small" @click="exportTable">导出</el-button>
        </div>
      </template>
      <div class="table-box">
        <FormTable :config="TableConfig_Detail"></FormTable>
      </div>
    </Panel>
  </div>
</template>
<script lang="ts" setup>
import Graphic from "@arcgis/core/Graphic.js";
import Point from "@arcgis/core/geometry/Point";
import Multipoint from "@arcgis/core/geometry/Multipoint";
import Polyline from "@arcgis/core/geometry/Polyline";
import Polygon from "@arcgis/core/geometry/Polygon";
import { GetFieldConfig } from '@/api/mapservice/fieldconfig';
import { formatDate } from '@/utils/DateFormatter';
import { formatterDate } from '@/utils/GlobalHelper';
import {
  excuteQuery,
  getCurrentPageOIDs,
  getGraphicLayer,
  initQueryParams,
  querySourceLayerIndex,
  setSymbol,
  gotoAndHighLight
} from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';
import { skipedFields } from '../../smartPipe/PipeManage/config';
import * as XLSX from 'xlsx';

const emit = defineEmits(['refreshed', 'close', 'refreshing', 'rowdblclick']);
const props = defineProps<{
  view?: __esri.MapView;
  /**
   * 查询地图服务的参数，
   * 具体参见initQueryParams
   */
  queryParams?: Record<string, any>;
  /**
   * tab标签
   */
  tabs?: {
    label: string;
    name: string;
    data?: any;
    layerid?: any;
    where?: string;
  }[];
  telport?: string;
  maxmin?: boolean;
  detailUrl?: string;
}>();
const staticState: {
  fieldConfig?: any;
  hilightLayer?: __esri.GraphicsLayer;
  tabFeatures: any[];
} = {
  tabFeatures: []
};
const refTab = ref<ISearchIns>();
const refPanel = ref<IPanelIns>();
const TableConfig_Detail = reactive<ITable>({
  dataList: [],
  columns: [{ label: 'OBJECTID', prop: 'OBJECTID' }],
  handleRowClick: (row) => {
    TableConfig_Detail.currentRow = row;
    if (props.view) {
      extentTo(props.view, refTab.value?.queryParams.type, row);
    } else {
      emit('rowdblclick', row, refTab.value?.queryParams?.type);
    }
  },
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig_Detail.pagination.page = page;
      TableConfig_Detail.pagination.limit = size;
      refreshDetail();
    }
  }
});
const TabFormConfig = reactive<ISearch>({
  filters: [
    {
      type: 'tabs',
      field: 'type',
      tabs: [],
      onChange: (val) => {
        refreshDetail(val, true);
      }
    }
  ]
});
const refreshDetail = async (tab?: any, showAll?: boolean) => {
  emit('refreshing');
  try {
    TableConfig_Detail.loading = true;
    tab = tab || refTab.value?.queryParams?.type;
    TableConfig_Detail.columns = [];
    TableConfig_Detail.dataList = [];
    if (!tab) {
      TableConfig_Detail.dataList = [];
      TableConfig_Detail.loading = false;
      return;
    }

    const propsTab = props.tabs?.find((item) => item.name === tab);
    const alloids = propsTab?.data;
    const keysArray = Object.keys(propsTab?.data[0].properties || {});
    TableConfig_Detail.columns = keysArray.map((item)=>{
      return {prop:item,label:item,minWidth:160}
    })
    let data = [...propsTab.data.map((item)=> {
      return {...item.properties,geometry:item.geometry}
    })]
    TableConfig_Detail.dataList = data;
    if (!alloids?.length) {
      staticState.hilightLayer?.removeAll();
    }
    if (showAll && alloids?.length) {
      const features = [];
      staticState.hilightLayer = getGraphicLayer(props.view, {
        id: 'pipe-detail',
        title: '详情展示'
      });
      if (staticState.hilightLayer) {
        staticState.hilightLayer.removeAll();
        alloids.map((item) => {
          const geometry = geoJsonToArcGIS(item.geometry);
          let graphic = new Graphic({
            geometry,
            attributes: item.properties,
            symbol: setSymbol(geometry.type)
          });
          features.push(graphic)
        })
        // allres.features.map((item) => (item.symbol = setSymbol(item.geometry.type)));
        staticState.hilightLayer.addMany(features);
        staticState.tabFeatures = features;
        TableConfig_Detail.pagination.hide = true;
      }
    }
  } catch (error) {
    console.dir(error);
    SLMessage.error('查询失败');
  }
  TableConfig_Detail.loading = false;
  emit('refreshed');
};
const extentTo = async (view: __esri.MapView, tab?: string, row?: any) => {
  tab = tab || refTab.value?.queryParams?.type;
  debugger
  row;
  const geometry = geoJsonToArcGIS(row.geometry);
  let feature = new Graphic({
    geometry,
    symbol: setSymbol(geometry.type)
  });
  if (feature) {
    staticState.hilightLayer?.add(feature);
    await gotoAndHighLight(view, feature);
  }
};
const clearData = () => {
  staticState.hilightLayer = getGraphicLayer(props.view, {
    id: 'pipe-detail',
    title: '详情展示'
  });
  TableConfig_Detail.dataList = [];
  TableConfig_Detail.pagination.total = 0;
  staticState.hilightLayer?.removeAll();
  const tab = TabFormConfig.filters?.find((item) => item.type === 'tabs') as ITabs;
  if (tab) {
    tab.tabs =
      props.tabs?.map((item) => {
        return {
          ...item,
          label: item.name,
          value: item.name
        };
      }) || [];
  }
};
const openDialog = async () => {
  if (!props.tabs?.length) {
    emit('refreshed');
    SLMessage.info('未查询到结果');
    return;
  }
  const tabField = TabFormConfig.filters?.find((item) => item.field === 'type') as ITabs;
  if (!tabField) return;
  tabField.tabs = props.tabs.map((item) => {
    return {
      ...item,
      value: item.name,
      data:null
    };
  });
  const tab = props.tabs[0].name;
  TabFormConfig.defaultParams = {
    type: tab
  };
  refTab.value?.resetForm();
  await refreshDetail(tab, true);
  refPanel.value?.Open();
};
const beforeClose = () => {
  staticState.hilightLayer?.removeAll();
  emit('close');
};
const closeDialog = () => {
  refPanel.value?.Close();
};
const geoJsonToArcGIS = (geoJson) => {
  switch (geoJson.type) {
    case 'Point':
      return new Point({
        x: geoJson.coordinates[0],
        y: geoJson.coordinates[1],
        spatialReference: { wkid: 3857 } // 你可以设置适当的 spatialReference
      });
    case 'MultiPoint':
      return new Multipoint({
        points: geoJson.coordinates,
        spatialReference: { wkid: 3857 }
      });
    case 'LineString':
      return new Polyline({
        paths: [geoJson.coordinates],
        spatialReference: { wkid: 3857 }
      });
    case 'MultiLineString':
      return new Polyline({
        paths: geoJson.coordinates,
        spatialReference: { wkid: 3857 }
      });
    case 'Polygon':
      return new Polygon({
        rings: geoJson.coordinates,
        spatialReference: { wkid: 3857 }
      });
    case 'MultiPolygon':
      return new Polygon({
        // 将 MultiPolygon 转换为单个 Polygon 对象的 rings 数组
        rings: geoJson.coordinates.reduce((rings, polygon) => rings.concat(polygon), []),
        spatialReference: { wkid: 3857 }
      });
    default:
      console.error('Unsupported GeoJSON type:', geoJson.type);
      return null;
  }
}
const exportTable = () => {
  if (!props.tabs || props.tabs.length === 0) {
    SLMessage.warning('无可导出的数据');
    return;
  }

  const wb = XLSX.utils.book_new();
  
  // 遍历所有图层数据
  props.tabs.forEach((tab, index) => {
    if (tab.data && tab.data.length > 0) {
      // 只导出属性字段，不导出geometry
      const exportData = tab.data.map(item => {
        // const { geometry, ...properties } = item;
        return item.properties;
      });
      
      const ws = XLSX.utils.json_to_sheet(exportData);
      XLSX.utils.book_append_sheet(wb, ws, tab.name || `图层${index + 1}`);
    }
  });
  
  XLSX.writeFile(wb, '管线明细数据.xlsx');
  SLMessage.success('导出成功');
};
defineExpose({
  openDialog,
  closeDialog,
  extentTo,
  clearData,
  exportTable
});
</script>
<style lang="scss" scoped>
.table-box {
  height: 100%;
}
.pipe-detail {
  &.filter-wrapper {
    padding: 0 8px;
    :deep(.el-form-item) {
      margin-bottom: 0;
    }
  }
}
</style>
<style lang="scss"></style>
