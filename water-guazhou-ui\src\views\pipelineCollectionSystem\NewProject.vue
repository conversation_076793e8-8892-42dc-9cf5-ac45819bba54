<!-- 管网数据采集系统-新建工程 -->
<template>
    <div class="new-project-page">
        <!-- 页面标题 -->
        <!-- <div class="page-title">
            <h2>{{ isEditMode ? '编辑工程' : '新建工程' }}</h2>
        </div> -->

        <!-- 表单区域 -->
        <div class="form-section">
            <!-- 第一行：工程全称、工程编号、工程简称、工程性质 -->
            <div class="form-row">
                <div class="form-item">
                    <label><span class="required">*</span>工程全称：</label>
                    <el-input v-model="formData.fullName" placeholder="请输入工程全称" />
                </div>
                <div class="form-item">
                    <label><span class="required">*</span>工程编号：</label>
                    <el-input v-model="formData.projectCode" placeholder="请输入工程编号" />
                </div>
                <div class="form-item">
                    <label><span class="required">*</span>工程简称：</label>
                    <el-input v-model="formData.shortName" placeholder="请输入工程简称" />
                </div>
                <div class="form-item">
                    <label><span class="required">*</span>工程性质：</label>
                    <el-select v-model="formData.nature" placeholder="请选择工程性质">
                        <el-option label="新建" value="new" />
                        <el-option label="扩建" value="expand" />
                        <el-option label="改造" value="rebuild" />
                        <el-option label="迁建" value="relocate" />
                    </el-select>
                </div>
            </div>

            <!-- 第二行：所属区划、测量单位、指派员工（1:1:2比例） -->
            <div class="form-row">
                <div class="form-item flex-1">
                    <label><span class="required">*</span>所属区划：</label>
                    <el-select v-model="formData.district" placeholder="请选择行政区划">
                        <el-option v-for="item in getDistrictOptions()" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </div>
                <div class="form-item flex-1">
                    <label><span class="required">*</span>测量单位：</label>
                    <el-input v-model="formData.measurementUnit" placeholder="请输入测量单位" />
                </div>
                <div class="form-item flex-2">
                    <label><span class="required">*</span>指派员工：</label>
                    <el-select v-model="formData.assignedStaff" multiple placeholder="请选择参与工程的员工" filterable clearable>
                        <el-option v-for="item in state.staffList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </div>
            </div>

            <!-- 第三行：工程范围、开始日期、结束日期 -->
            <div class="form-row">
                <div class="form-item">
                    <label><span class="required">*</span>工程范围：</label>
                    <div class="drawing-controls">
                        <el-button type="primary" @click="startDrawing" :disabled="isDrawing"
                            :class="{ 'is-loading': isDrawing }">
                            <template v-if="isDrawing">
                                <el-icon class="is-loading">
                                    <Loading />
                                </el-icon>
                                绘制中...
                            </template>
                            <template v-else-if="hasDrawnArea">
                                重新绘制
                            </template>
                            <template v-else>
                                开始绘制
                            </template>
                        </el-button>
                        <el-button v-if="isDrawing" type="default" @click="cancelDrawing" >
                            取消绘制
                        </el-button>
                        <!-- <el-button v-if="!isDrawing && hasDrawnArea" :type="isSketchVisible ? 'warning' : 'success'" 
                            @click="toggleSketch" size="small">
                            {{ isSketchVisible ? '隐藏编辑工具' : '显示编辑工具' }}
                        </el-button> -->
                        <!-- <el-button v-if="hasDrawnArea" type="danger" @click="clearDrawnArea" size="small">
                            清除范围
                        </el-button> -->
                    </div>
        
                </div>
                <div class="form-item">
                    <label>开始日期：</label>
                    <el-date-picker v-model="formData.startDate" type="date" placeholder="请选择开始日期" format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD" />
                </div>
                <div class="form-item">
                    <label>结束日期：</label>
                    <el-date-picker v-model="formData.endDate" type="date" placeholder="请选择结束日期" format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD" />
                </div>
            </div>
            <!-- 地图区域 -->
            <div class="map-section">
                <FormMap 
                    ref="refFormMap" 
                    v-model="state.workScope" 
                    :show-input="false" 
                    @change="handleMapChange"
                    @sketch-create="onSketchCreate"
                    @sketch-update="onSketchUpdate"
                    @sketch-delete="onSketchDelete"
                    @draw-complete="onDrawingComplete" 
                    @loaded="onMapLoaded" 
                    @draw-start="onDrawStart"
                    class="project-map" 
                />
            </div>
            <!-- 底部操作按钮 -->
            <div class="footer-actions">
                <el-button type="primary" @click="handleSubmit" :loading="isSubmitting">
                    {{ isEditMode ? '更新' : '确定' }}
                </el-button>
                <el-button @click="handleCancel">
                    取消
                </el-button>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { reactive, ref, onMounted, watch } from 'vue';
import { SLConfirm, SLMessage } from '@/utils/Message';
import { createProject, updateProject, generateProjectCode, validateProjectCode } from '@/api/pipelineCollectionSystem/project';
import { getAvailableStaff } from '@/api/pipelineCollectionSystem/staff';
import { getUserslistByAuth } from '@/api/user';
import { useUserStore } from '@/store';
import { removeSlash } from '@/utils/removeIdSlash';
import FormMap from '@/components/arcMap/FormMap.vue';
import { Loading, CircleCheck, InfoFilled } from '@element-plus/icons-vue';
import {
    getProjectNatureOptions,
    getDistrictOptions,
    getMeasurementUnitOptions,
    PROJECT_VALIDATION_RULES,
    getDefaultProjectFormValues
} from './config';

// Props 定义
const props = defineProps<{
  projectData?: any; // 编辑时传入的工程数据
}>();

// 事件定义
const emit = defineEmits<{
  success: [];
  cancel: [];
}>();

// 是否为编辑模式
const isEditMode = ref(!!props.projectData);

const refForm = ref<IFormIns>();
const refFormMap = ref<InstanceType<typeof FormMap>>();
const userStore = useUserStore();

const state = reactive<{
    workScope: any;
    staffList: any[];
    generatingCode: boolean;
}>({
    workScope: undefined,
    staffList: [],
    generatingCode: false
});

// 表单数据
const formData = reactive({
    fullName: '',
    projectCode: '',
    shortName: '',
    nature: '',
    district: '',
    measurementUnit: '',
    startDate: '',
    endDate: '',
    assignedStaff: [] as string[]
});

// 初始化表单数据
const initFormData = () => {
    if (props.projectData) {
        Object.keys(formData).forEach(key => {
            if (props.projectData[key] !== undefined) {
                if (key === 'assignedStaff' && typeof props.projectData[key] === 'string') {
                    // 如果 assignedStaff 是字符串，转换为数组
                    formData[key] = props.projectData[key].split(',').filter(Boolean);
                } else {
                    formData[key] = props.projectData[key];
                }
            }
        });
        
        // 设置工程范围数据
        if (props.projectData.workScopeCoordinates) {
            state.workScope = props.projectData.workScopeCoordinates;
            hasDrawnArea.value = true;
        }
    }
};

// 监听 props 变化
watch(() => props.projectData, (newData) => {
    if (newData) {
        isEditMode.value = true;
        initFormData();
    } else {
        isEditMode.value = false;
    }
}, { immediate: true });

// 其他状态
const isDrawing = ref(false);
const isSubmitting = ref(false);
const hasDrawnArea = ref(false); // 是否已经绘制过范围
const isSketchVisible = ref(false); // Sketch 工具是否可见

// 开始绘制/重新绘制
const startDrawing = () => {
    if (hasDrawnArea.value) {
        // 重新绘制：清除之前的图形
        clearDrawnArea();
    }

    // 触发地图绘制功能
    if (refFormMap.value) {
        startPolygonDrawing();
    }
};

// 显示 Sketch 工具
const showSketchTool = () => {
    if (refFormMap.value) {
        try {
            refFormMap.value.showSketch();
            isSketchVisible.value = true;
        } catch (error) {
            console.error('显示 Sketch 工具失败:', error);
        }
    }
};

// 隐藏 Sketch 工具
const hideSketchTool = () => {
    if (refFormMap.value) {
        try {
            refFormMap.value.hideSketch();
            isSketchVisible.value = false;
        } catch (error) {
            console.error('隐藏 Sketch 工具失败:', error);
        }
    }
};

// 切换 Sketch 工具显隐
const toggleSketch = () => {
    if (refFormMap.value) {
        try {
            refFormMap.value.toggleSketch();
            // 延迟获取状态，确保切换操作完成
            setTimeout(() => {
                if (refFormMap.value) {
                    isSketchVisible.value = refFormMap.value.isSketchVisible();
                }
            }, 50);
        } catch (error) {
            console.error('切换 Sketch 工具失败:', error);
        }
    }
};

// 清除绘制区域
const clearDrawnArea = () => {
    state.workScope = undefined;
    hasDrawnArea.value = false;
    isDrawing.value = false;

    // 清除地图上的图形
    if (refFormMap.value) {
        clearMapGraphics();
    }
};

// 开始多边形绘制
const startPolygonDrawing = () => {
    const mapComponent = refFormMap.value;
    if (mapComponent) {
        try {
            // 确保地图已加载完成再开始绘制
            const view = mapComponent.getView();
            if (view) {
                // 使用新的 Sketch widget 方法
                mapComponent.startCreate('polygon');
                isDrawing.value = true;
            } else {
                // 延迟重试
                setTimeout(() => {
                    const retryView = mapComponent.getView();
                    if (retryView) {
                        mapComponent.startCreate('polygon');
                        isDrawing.value = true;
                    } else {
                        console.error('地图加载超时，使用模拟绘制');
                        simulateDrawing();
                    }
                }, 1000);
            }
        } catch (error) {
            console.error('调用绘制方法失败:', error);
            simulateDrawing();
        }
    } else {
        console.error('地图组件引用不存在，使用模拟绘制');
        simulateDrawing();
    }
};

// 模拟绘制过程（用于开发阶段）
const simulateDrawing = () => {
    isDrawing.value = true;
    // 模拟绘制时间
    setTimeout(() => {
        // 模拟绘制完成，返回示例几何数据
        const mockGeometry = {
            type: 'polygon',
            rings: [[
                [116.3974, 39.9093],
                [116.4174, 39.9093],
                [116.4174, 39.9293],
                [116.3974, 39.9293],
                [116.3974, 39.9093]
            ]],
            spatialReference: { wkid: 4326 }
        };

        onDrawingComplete(mockGeometry);
    }, 1500);
};

// 清除地图图形
const clearMapGraphics = () => {
    // 调用FormMap组件的清除方法
    const mapComponent = refFormMap.value;
    if (mapComponent) {
        try {
            mapComponent.clearGraphics();
        } catch (error) {
            console.error('调用清除方法失败:', error);
        }
    }
};

// Sketch 创建事件处理
const onSketchCreate = (event: any) => {
    console.log('Sketch 创建事件:', event);
    isDrawing.value = false;
    hasDrawnArea.value = true;
    state.workScope = event.geometry;
    
    // 同步 Sketch 可见状态
    setTimeout(() => {
        if (refFormMap.value) {
            isSketchVisible.value = refFormMap.value.isSketchVisible();
        }
    }, 100);
    
    SLMessage.success('范围绘制完成');
};

// Sketch 更新事件处理
const onSketchUpdate = (event: any) => {
    console.log('Sketch 更新事件:', event);
    state.workScope = event.geometry;
    SLMessage.info('范围已更新');
};

// Sketch 删除事件处理
const onSketchDelete = (event: any) => {
    console.log('Sketch 删除事件:', event);
    state.workScope = undefined;
    hasDrawnArea.value = false;
    SLMessage.info('范围已删除');
};

// 绘制完成回调（保持向后兼容）
const onDrawingComplete = (geometryData: any) => {
    isDrawing.value = false;
    hasDrawnArea.value = true;
    state.workScope = geometryData;
    
    // 同步 Sketch 可见状态
    setTimeout(() => {
        if (refFormMap.value) {
            isSketchVisible.value = refFormMap.value.isSketchVisible();
        }
    }, 100);
    
    SLMessage.success('范围绘制完成');
};

// 地图加载完成处理
const onMapLoaded = (view: any) => {
    console.log('地图加载完成');
    // 地图加载完成后，获取初始的 sketch 可见状态
    setTimeout(() => {
        if (refFormMap.value) {
            isSketchVisible.value = refFormMap.value.isSketchVisible();
        }
    }, 100);
};

// 绘制开始处理
const onDrawStart = () => {
    console.log('绘制已开始');
    isDrawing.value = true;
};

// 取消绘制
const cancelDrawing = () => {
    const mapComponent = refFormMap.value;
    if (mapComponent) {
        try {
            // 调用地图组件的停止绘制方法
            mapComponent.stopDrawing();
            isDrawing.value = false;
            console.log('绘制已取消');
        } catch (error) {
            console.error('取消绘制失败:', error);
            isDrawing.value = false;
        }
    } else {
        isDrawing.value = false;
    }
};

// 地图数据变化处理
const handleMapChange = (val: any) => {
    state.workScope = val;
    // 如果有数据，标记为已绘制
    if (val && !isDrawing.value) {
        hasDrawnArea.value = true;
    }
};

// 工程相关选项已从配置文件导入

// 生成工程编号
const generateCode = async () => {
    try {
        state.generatingCode = true;
        const res = await generateProjectCode();
        if (res.data?.code === 200 && res.data?.data) {
            if (refForm.value?.dataForm) {
                refForm.value.dataForm.projectCode = res.data.data;
            }
            SLMessage.success('工程编号生成成功');
        } else {
            SLMessage.error('工程编号生成失败');
        }
    } catch (error: any) {
        SLMessage.error(error.message || '工程编号生成失败');
    } finally {
        state.generatingCode = false;
    }
};

// 验证工程编号
const validateCode = async (code: string) => {
    if (!code) return true;
    try {
        const res = await validateProjectCode(code);
        return res.data?.data?.isValid !== false;
    } catch (error) {
        console.error('验证工程编号时出错:', error);
        return true;
    }
};

// 处理表单提交
const handleSubmit = async () => {
    if (isSubmitting.value) return;

    try {
        isSubmitting.value = true;

        // 验证必填字段
        const requiredFields = [
            { field: 'fullName', name: '工程全称' },
            { field: 'projectCode', name: '工程编号' },
            { field: 'shortName', name: '工程简称' },
            { field: 'nature', name: '工程性质' },
            { field: 'district', name: '所属区划' },
            { field: 'measurementUnit', name: '测量单位' },
            { field: 'assignedStaff', name: '指派员工' }
        ];

        for (const { field, name } of requiredFields) {
            const value = formData[field];
            if (!value || (Array.isArray(value) && value.length === 0)) {
                SLMessage.error(`请填写${name}`);
                return;
            }
        }

        // 验证工程范围
        if (!state.workScope || (Array.isArray(state.workScope) && state.workScope.length === 0)) {
            SLMessage.error('请绘制工程范围');
            return;
        }

        const submitData = {
            ...formData,
            assignedStaff: formData.assignedStaff.join(','),
            workScopeCoordinates: state.workScope ?
                (Array.isArray(state.workScope) ? state.workScope.join(',') : state.workScope) : '',
            createUserId: userStore.user?.id?.id,
            createTime: new Date().toISOString()
        };

        let res;
        if (isEditMode.value && props.projectData?.id) {
            // 编辑模式
            res = await updateProject(props.projectData.id, submitData);
        } else {
            // 创建模式
            res = await createProject(submitData);
        }

        if (res.data?.code === 200) {
            const message = isEditMode.value ? '工程更新成功' : '工程创建成功';
            SLMessage.success(message);
            
            // 发射成功事件
            emit('success');
            
            if (!isEditMode.value) {
                // 只有在创建模式下才重置表单
                Object.keys(formData).forEach(key => {
                    if (key === 'assignedStaff') {
                        formData[key] = [];
                    } else {
                        formData[key] = '';
                    }
                });

                // 清除绘制状态和地图图形
                state.workScope = undefined;
                hasDrawnArea.value = false;
                isDrawing.value = false;
                isSketchVisible.value = false;
                clearMapGraphics();
            }
        } else {
            const message = isEditMode.value ? '工程更新失败' : '工程创建失败';
            SLMessage.error(res.data?.message || message);
        }
    } catch (error: any) {
        SLMessage.error(error.message || '工程创建失败');
    } finally {
        isSubmitting.value = false;
    }
};

// 取消操作
const handleCancel = () => {
    emit('cancel');
};

// 初始化员工选项
const initStaffOptions = async () => {
    try {
        const res = await getAvailableStaff();
        if (res.data?.code === 200 && res.data?.data) {
            state.staffList = res.data.data.map(staff => ({
                label: staff.name || staff.firstName,
                value: removeSlash(staff.id || staff.staffId)
            }));
        }
    } catch (error) {
        console.error('获取员工列表失败:', error);
        // 如果专用API失败，尝试使用通用用户API
        try {
            const res = await getUserslistByAuth({
                authType: 'CUSTOMER_USER'
            });

            state.staffList = res.data.map(user => ({
                label: user.firstName,
                value: removeSlash(user.id.id)
            }));
        } catch (fallbackError) {
            console.error('获取用户列表也失败:', fallbackError);
            SLMessage.warning('获取员工列表失败，请刷新页面重试');
        }
    }
};

onMounted(() => {
    initStaffOptions();
});
</script>
<style lang="scss" scoped>
.new-project-page {
    padding: 20px;
    background: #f5f7fa;
    // height: 60vh;
}

.page-title {
    margin-bottom: 20px;

    h2 {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin: 0;
    }
}

.form-section {
    background: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;

    &:last-child {
        margin-bottom: 0;
    }
}

.form-item {
    flex: 1;
    min-width: 200px;
    display: flex;
    align-items: flex-start;
    gap: 12px;

    &.full-width {
        flex: 1;
        min-width: 100%;
    }

    &.flex-1 {
        flex: 1;
    }

    &.flex-2 {
        flex: 2;
    }

    label {
        flex-shrink: 0;
        min-width: 80px;
        font-weight: 500;
        color: #606266;
        font-size: 14px;
        text-align: right;
        margin: 0;
        margin-top: 8px;

        .required {
            color: #f56c6c;
            margin-right: 2px;
        }
    }

    .el-input,
    .el-select,
    .el-date-picker {
        flex: 1;
    }

    .el-button {
        flex: 1;
    }

    .drawing-controls {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;
    }

    .draw-status {
        margin-left: 12px;
        color: #67c23a;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 4px;
        white-space: nowrap;
    }

    .draw-tips {
        width: 100%;
        margin-top: 8px;
        color: #409eff;
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
        background: #f0f8ff;
        padding: 6px 10px;
        border-radius: 4px;
        border-left: 3px solid #409eff;
    }
}

.map-section {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;

    .project-map {
        width: 100%;
        height: 400px;
        border-radius: 6px;
        overflow: hidden;
    }
}

.footer-actions {
    text-align: center;

    .el-button {
        min-width: 120px;
        height: 40px;
    }
}

@media (max-width: 768px) {
    .new-project-page {
        padding: 12px;
    }

    .form-row {
        flex-direction: column;
        gap: 16px;
    }

    .form-item {
        min-width: auto;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        &.full-width,
        &.flex-1,
        &.flex-2 {
            flex: 1;
        }

        label {
            text-align: left;
            min-width: auto;
            margin-top: 0;
        }

        .drawing-controls {
            width: 100%;
        }
    }
}
</style>